package com.tcl.ai.note.handwritingtext.ui.popup

import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import com.sunia.penengine.sdk.operate.touch.PenProp
import com.tcl.ai.note.handwritingtext.bean.MenuBarItem
import com.tcl.ai.note.handwritingtext.bean.PenColor
import com.tcl.ai.note.handwritingtext.ui.pen.PenToolbar
import com.tcl.ai.note.handwritingtext.utils.defShadow
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.getStatusBarHeight
import com.tcl.ai.note.utils.px2dp
import com.tcl.ai.note.utils.toPx


/**
 * 笔刷工具栏
 */
@Composable
fun TabletToolPopup(
    menuBarItem: MenuBarItem,
    onSwitchBrush:(PenProp) -> Unit,
    onChangeBrushSize:(brushSize:Float) -> Unit,
    onChangePenColor:(penColor:PenColor) -> Unit,
    onOpenColorPicker: () -> Unit,
    onDismissRequest: () -> Unit
) {
    val dimens = getGlobalDimens()
    val space = 23.dp
    val popupWidth: Dp = 328.dp

    val density = LocalDensity.current
    val layoutDirection = LocalLayoutDirection.current
    val screenWidthDp = LocalConfiguration.current.screenWidthDp
    val position: Offset = menuBarItem.position
    val xOffset = with(density) {
        if (layoutDirection == LayoutDirection.Rtl) {
            (screenWidthDp.dp.toPx() - position.x - dimens.btnSize.toPx / 2 - popupWidth.toPx() / 2).toInt()
        } else {
            (menuBarItem.position.x + dimens.btnSize.toPx / 2 - popupWidth.toPx() / 2).toInt()

        }
    }
    val yOffset =(space.toPx+position.y).toInt()
    BounceScalePopup(
        onDismissRequest ={
            onDismissRequest()
        } ,
        offset = IntOffset(xOffset,yOffset),
        ) { closePopup ->
        Box(
            modifier = Modifier
                .width(popupWidth)
                .defShadow(radius = 20.dp)
        ) {
            Row(
                modifier = Modifier.background(color = TclTheme.colorScheme.tertiaryBackground)
                    .width(popupWidth))
            {
                PenToolbar(
                    modifier =  Modifier.fillMaxWidth(),
                    onOpenColorPicker = {
                        onOpenColorPicker()
                        closePopup()
                    },
                    onSwitchBrush = onSwitchBrush,
                    onChangePenColor = onChangePenColor,
                    onChangeBrushSize = onChangeBrushSize,
                )
            }
        }

    }
}


@Composable
fun PhoneToolPopup(
    isDarkTheme: Boolean = isSystemInDarkTheme(),
    areaHeight: Int,
    onSwitchBrush:(PenProp) -> Unit,
    onChangeBrushSize:(brushSize:Float) -> Unit,
    onChangePenColor:(penColor:PenColor) -> Unit,
    onOpenColorPicker: () -> Unit,
    onDismissRequest: () -> Unit
) {
    val space = 8.dp
    val popupWidth: Dp = 328.dp
    val popupHeight:Dp = 272.dp

    val density = LocalDensity.current
    val screenWidthDp = LocalConfiguration.current.screenWidthDp

    val xOffset = with(density) {
        (screenWidthDp.dp.toPx() - popupWidth.toPx())/2
    }




    val yOffset =areaHeight - popupHeight.toPx - space.toPx

    SlideFromBottomPopup(
        onDismissRequest ={
            onDismissRequest()
        } ,
        offset = IntOffset(xOffset.toInt(),yOffset.toInt()),
    ) { closePopup ->
        Box(
            modifier = Modifier
                .width(popupWidth)
                .height(popupHeight)
                .defShadow(20.dp)
        ) {
            Row(
                modifier = Modifier.background(color = TclTheme.colorScheme.tertiaryBackground, shape = RoundedCornerShape(20.dp))
                    .width(popupWidth))
            {
                PenToolbar(
                    modifier =  Modifier.fillMaxWidth(),
                    isDarkTheme = isDarkTheme,
                    onOpenColorPicker = {
                        closePopup()
                        onOpenColorPicker()

                    },
                    onSwitchBrush = onSwitchBrush,
                    onChangePenColor = onChangePenColor,
                    onChangeBrushSize = onChangeBrushSize,
                )
            }
        }

    }


}