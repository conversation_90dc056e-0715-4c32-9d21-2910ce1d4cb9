package com.tcl.ai.note.handwritingtext.ui.image

import android.Manifest
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.core.app.ActivityCompat.shouldShowRequestPermissionRationale
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.CameraPermissionDialog
import com.tcl.ai.note.handwritingtext.utils.ImagePickerManager
import com.tcl.ai.note.utils.AppActivityManager
import kotlinx.coroutines.flow.update

@Composable
fun rememberPickImageHelper(
    context: Context,
    imageCount: Int,
    hitText: String,
    onSelectImage: (Uri) -> Unit
): (action: () -> Unit) -> Unit {
    val pickSingleMedia = rememberLauncherForActivityResult(
        ActivityResultContracts.PickVisualMedia()
    ) { uri  ->
        uri?.let {
            // 插入前再次检查数量
            if (imageCount >= InsertImage.MAX_IMAGE_LIMIT) {
                Toast.makeText(context, hitText, Toast.LENGTH_SHORT).show()
                return@rememberLauncherForActivityResult
            }
            onSelectImage(it)
        }
    }
    return {
        pickSingleMedia.launch(
            PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly)
        )
    }
}

@Composable
fun rememberTakePhotoHelper(
    context: Context,
    imageCount: Int,
    hitText: String,
    onSelectImage: (Uri) -> Unit
): (action: () -> Unit) -> Unit {

    var showCameraPermissionDialog by remember { mutableStateOf(false) }

    var takePhotoLauncher: ActivityResultLauncher<Uri>

    // 先初始化图片选择启动器（无依赖）
    val pickImagesLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri  ->
        uri?.let {
            // 插入前再次检查数量
            if (imageCount >= InsertImage.MAX_IMAGE_LIMIT) {
                Toast.makeText(context, hitText, Toast.LENGTH_SHORT).show()
                return@rememberLauncherForActivityResult
            }
            onSelectImage(it)
        }
    }


    // 初始化管理器（此时只传入 pickImagesLauncher）
    val imagePickerManager = remember {
        ImagePickerManager(
            context = context,
            pickImagesLauncher = pickImagesLauncher
        ).apply {
            // 4. 配置拍照结果处理
            setPhotoCallback { uri ->
                onSelectImage(uri)
            }
        }
    }

    // 最后初始化拍照启动器（此时可以安全引用管理器）
    takePhotoLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.TakePicture()
    ) { success ->
        // 插入前再次检查数量
        if (imageCount >= InsertImage.MAX_IMAGE_LIMIT) {
            Toast.makeText(context, hitText, Toast.LENGTH_SHORT).show()
            return@rememberLauncherForActivityResult
        }
        if (success) {
            imagePickerManager.handleTakePhotoResult()
            AppActivityManager.isTakingPhoto.update { false }
        }
    }

    // 为管理器注入启动器
    imagePickerManager.takePhotoLauncher = takePhotoLauncher

    // 相机权限请求Launcher
    // 处理CAMERA权限请求结果
    val cameraPermissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            // 权限授予后执行拍照
            imagePickerManager.takePhoto()
        } else {
            if (context is ComponentActivity
                && !shouldShowRequestPermissionRationale(context, Manifest.permission.CAMERA)
            ) {
                // 更新状态以显示理由对话框
                showCameraPermissionDialog = true
            }
        }
    }

    if(showCameraPermissionDialog){
        CameraPermissionDialog(
            onDismiss = {showCameraPermissionDialog = false},
            onGoToSettings = {
                showCameraPermissionDialog = false
                if (context is ComponentActivity) {
                    toAppSetting(context)
                }
            }
        )
    }

    return {
        cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
    }
}

private fun toAppSetting(activity: ComponentActivity) {
    val intent = Intent().apply {
        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
        data = Uri.fromParts("package", activity.packageName, null)
    }
    activity.startActivity(intent)
}
