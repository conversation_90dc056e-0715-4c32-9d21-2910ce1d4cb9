package com.tcl.ai.note.handwritingtext.ui.edit.richtexttoolbar.popup

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.foundation.layout.Box
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.foundation.focusable
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.isShiftPressed
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.onKeyEvent
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import kotlinx.coroutines.delay
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarItem
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarState
import com.tcl.ai.note.handwritingtext.bean.RichTextToolType
import com.tcl.ai.note.handwritingtext.ui.richtext.component.BackgroundColorPicker
import com.tcl.ai.note.handwritingtext.ui.richtext.component.TextColorPicker


/**
 * 渲染颜色选择器弹出框
 * 根据工具类型显示相应的颜色选择器弹窗
 *
 * @param item 颜色选择器项
 * @param state 工具栏状态
 * @param offsetYPx 弹出框Y轴偏移量（像素）
 */
@Composable
internal fun RenderColorPickerPopup(
    item: RichTextToolBarItem.ColorPicker,
    state: RichTextToolBarState,
    offsetYPx: Int
) {
    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current
    
    when (item.toolType) {
        RichTextToolType.TEXT_COLOR -> {
            if (state.showTextColorPicker) {
                Popup(
                    alignment = Alignment.TopCenter,
                    offset = IntOffset(0, -offsetYPx),
                    properties = PopupProperties(
                        focusable = true,
                        dismissOnBackPress = true,
                        dismissOnClickOutside = true
                    )
                ) {
                    Box(
                        modifier = Modifier
                            .focusRequester(focusRequester)
                            .focusable()
                            .onKeyEvent { keyEvent ->
                                when (keyEvent.key) {
                                    Key.Escape -> {
                                        // 关闭颜色选择器的逻辑需要通过 item 的回调来实现
                                        true
                                    }
                                    Key.Tab -> {
                                        if (keyEvent.isShiftPressed) {
                                            focusManager.moveFocus(androidx.compose.ui.focus.FocusDirection.Previous)
                                        } else {
                                            focusManager.moveFocus(androidx.compose.ui.focus.FocusDirection.Next)
                                        }
                                        true
                                    }
                                    else -> false
                                }
                            }
                    ) {
                        TextColorPicker(
                            selectedColor = state.textColor,
                            onColorSelected = { color -> item.onColorChange(color, item) }
                        )
                    }
                }
                
                LaunchedEffect(Unit) {
                    delay(100)
                    focusRequester.requestFocus()
                }
            }
        }
        RichTextToolType.TEXT_BG_COLOR -> {
            if (state.showTextBgColorPicker) {
                Popup(
                    alignment = Alignment.TopCenter,
                    offset = IntOffset(0, -offsetYPx),
                    properties = PopupProperties(
                        focusable = true,
                        dismissOnBackPress = true,
                        dismissOnClickOutside = true
                    )
                ) {
                    Box(
                        modifier = Modifier
                            .focusRequester(focusRequester)
                            .focusable()
                            .onKeyEvent { keyEvent ->
                                when (keyEvent.key) {
                                    Key.Escape -> {
                                        // 关闭颜色选择器的逻辑需要通过 item 的回调来实现
                                        true
                                    }
                                    Key.Tab -> {
                                        if (keyEvent.isShiftPressed) {
                                            focusManager.moveFocus(androidx.compose.ui.focus.FocusDirection.Previous)
                                        } else {
                                            focusManager.moveFocus(androidx.compose.ui.focus.FocusDirection.Next)
                                        }
                                        true
                                    }
                                    else -> false
                                }
                            }
                    ) {
                        BackgroundColorPicker(
                            selectedColor = state.textBgColor,
                            onColorSelected = { color -> item.onColorChange(color, item) }
                        )
                    }
                }
                
                LaunchedEffect(Unit) {
                    delay(100)
                    focusRequester.requestFocus()
                }
            }
        }
        else -> {
            // 其他类型不处理
        }
    }
}