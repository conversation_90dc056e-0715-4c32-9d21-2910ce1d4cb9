package com.tcl.ai.note.handwritingtext.database.entity

/**
 * 首屏缩略图类型
 * 对应数据库中字段 handwritingThumbnail
 */
enum class FirstScreenThumbnailType {
    IMAGE, HAND_DRAWN;

    fun toDBString(): String = when (this) {
        IMAGE      -> "IMAGE"
        HAND_DRAWN -> "HAND_DRAWN"
    }
    companion object {

        fun fromDBString(str: String?): FirstScreenThumbnailType = when (str?.trim()?.uppercase()) {
            "IMAGE"      -> IMAGE
            "HAND_DRAWN" -> HAND_DRAWN
            else         -> HAND_DRAWN
        }
    }
}