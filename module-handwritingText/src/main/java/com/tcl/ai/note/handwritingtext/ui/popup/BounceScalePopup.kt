package com.tcl.ai.note.handwritingtext.ui.popup

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.isShiftPressed
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.onKeyEvent
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import kotlinx.coroutines.delay

/**
 * 带有弹性缩放动画效果的弹出组件
 * 从小到大弹出
 */
@Composable
fun BounceScalePopup(
    onDismissRequest: () -> Unit,
    offset: IntOffset,
    durationMillis:Int = 200,
    alignment: Alignment = Alignment.TopStart,
    enterTransformOrigin: TransformOrigin = TransformOrigin(0.5f, 0f),
    exitTransformOrigin: TransformOrigin = TransformOrigin(0.5f, 0f),
    content: @Composable (closePupup:() ->Unit) -> Unit
) {
    var showContent by remember { mutableStateOf(false) }
    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current
    
    LaunchedEffect(showContent) {
        if (!showContent) {
            delay(durationMillis.toLong())
            onDismissRequest()
        }
    }

    Popup(
        alignment = alignment,
        onDismissRequest = {
            showContent = false
        },
        offset = offset,
        properties = PopupProperties(
            focusable = true,
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {

        DisposableEffect(Unit) {
            showContent = true
            onDispose {}
        }

        AnimatedVisibility(
            visible = showContent,
            enter = scaleIn(
                initialScale = 0.1f,
                animationSpec = spring(
                    stiffness = Spring.StiffnessLow,
                    dampingRatio = Spring.DampingRatioLowBouncy
                ),
                transformOrigin = enterTransformOrigin
            ),
            exit = scaleOut(
                targetScale = 0f,
                animationSpec = tween(
                    durationMillis = durationMillis,
                    easing = FastOutSlowInEasing
                ),
                transformOrigin = exitTransformOrigin
            ),
            modifier = Modifier
                .focusRequester(focusRequester)
                .onKeyEvent { keyEvent ->
                    when (keyEvent.key) {
                        Key.Escape -> {
                            showContent = false
                            true
                        }
                        Key.Tab -> {
                            if (keyEvent.isShiftPressed) {
                                focusManager.moveFocus(FocusDirection.Previous)
                            } else {
                                focusManager.moveFocus(FocusDirection.Next)
                            }
                            true
                        }
                        else -> false
                    }
                }
        ) {

            content{
                showContent = false
            }
        }
        
        // 专门用于焦点请求的LaunchedEffect
        LaunchedEffect(Unit) {
            delay(300) // 稍微增加延迟确保动画完成
            try {
                focusRequester.requestFocus()
            } catch (e: Exception) {
                // 忽略焦点请求失败的异常
            }
        }
    }
}

    val BounceScaleCenter = TransformOrigin(0.5f, 0.5f)
    val BounceScaleRightTop = TransformOrigin(1f, 0f)
