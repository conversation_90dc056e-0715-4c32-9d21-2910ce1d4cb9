package com.tcl.ai.note.handwritingtext.vm

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.handwritingtext.richtext.data.RichTextStyleEntity
import com.tcl.ai.note.handwritingtext.state.EditMode
import com.tcl.ai.note.handwritingtext.ui.utils.scale.MatrixInfo
import com.tcl.ai.note.handwritingtext.utils.ShareContentUtils
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import android.view.View
import com.tcl.ai.note.handwritingtext.database.entity.BgMode
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.delegate
import com.tcl.ai.note.utils.delegateFunc
import com.tcl.ai.note.utils.isLandScapeByScreenSize
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class TextAndDrawViewModel : ViewModel() {
    // 缩放位移信息
    private val defaultMatrixInfo = MatrixInfo(scale = if (isLandScapeByScreenSize) 1.2f else 1.0f)
    private val mMatrixInfoState = MutableStateFlow(defaultMatrixInfo)
    val matrixInfoState = mMatrixInfoState.asStateFlow()
    var matrixInfo by mMatrixInfoState.delegate()

    // 缩放大小
    val scale get() = matrixInfo.scale

    // 编辑模式
    private val mEditModeState = MutableStateFlow<EditMode>(EditMode.TEXT)
    val editModeState = mEditModeState.asStateFlow()
    var editMode by mEditModeState.delegate()

    val onLoading = MutableStateFlow(false)

    // 锁屏前的编辑状态保存
    private var savedEditState: EditStateSnapshot? = null

    private val mShareLoadingState = MutableStateFlow(false)
    val shareLoadingState = mShareLoadingState.asStateFlow()
    private var lastShareTime = System.currentTimeMillis()
    private var shareJob: Job? = null
    private var showLoadingJob: Job? = null

    /**
     * 编辑状态快照，用于锁屏恢复
     */
    data class EditStateSnapshot(
        val editMode: EditMode,
        val isKeyboardActive: Boolean,
        val hasFocus: Boolean,
        val isKeyboardVisible: Boolean, // 键盘是否实际可见
        val cursorPosition: Int = 0
    )

    /**
     * 保存当前编辑状态（锁屏前调用）
     */
    fun saveEditState(
        isKeyboardActive: Boolean,
        hasFocus: Boolean,
        isKeyboardVisible: Boolean,
        cursorPosition: Int = 0
    ) {
        savedEditState = EditStateSnapshot(
            editMode = editMode,
            isKeyboardActive = isKeyboardActive,
            hasFocus = hasFocus,
            isKeyboardVisible = isKeyboardVisible,
            cursorPosition = cursorPosition
        )
    }

    /**
     * 获取保存的编辑状态
     */
    fun getSavedEditState(): EditStateSnapshot? = savedEditState

    /**
     * 清除保存的编辑状态
     */
    fun clearSavedEditState() {
        savedEditState = null
    }

    /**
     * 检查是否需要恢复编辑状态
     */
    fun shouldRestoreEditState(): Boolean {
        return savedEditState?.let {
            it.hasFocus && it.editMode == EditMode.TEXT
        } == true
    }

    fun shareImage(
        content: String,
        style: RichTextStyleEntity,
        bgMode: BgMode,
        bgColor: Long,
        isDark: Boolean,
        suniaDrawViewModel: SuniaDrawViewModel,
        context: Context,
    ) {
        if (isShareTimeInLag()) {
            return
        }
        shareJob?.cancel()
        shareJob = viewModelScope.launch {
            restartShowLoadingJob()
            try {
                val bitmap = ShareContentUtils.saveTextAndDrawToBitmap(
                    content,
                    style,
                    bgMode,
                    bgColor,
                    isDark,
                    suniaDrawViewModel
                )
                val file = ShareContentUtils.getBitmapFileForShare(bitmap)
                ShareContentUtils.startImageShareIntent(file, context)
            } catch (ex: Exception) {
                Logger.e(TAG, "shareImage error, ${ex.javaClass}, ${ex.message}")
            } finally {
                dismissLoadingJobWithDelay()
            }
        }
    }

    fun sharePdf(
        content: String,
        style: RichTextStyleEntity,
        bgMode: BgMode,
        bgColor: Long,
        isDark: Boolean,
        suniaDrawViewModel: SuniaDrawViewModel,
        context: Context,
    ) {
        if (isShareTimeInLag()) {
            return
        }
        shareJob?.cancel()
        shareJob = viewModelScope.launch {
            restartShowLoadingJob()
            try {
                val bitmap = ShareContentUtils.saveTextAndDrawToBitmap(
                    content,
                    style,
                    bgMode,
                    bgColor,
                    isDark,
                    suniaDrawViewModel
                )
                val file = ShareContentUtils.getPdfFileForShare(bitmap)
                ShareContentUtils.startPdfShareIntent(file, context)
            } catch (ex: Exception) {
                Logger.e(TAG, "sharePdf error, ${ex.javaClass}, ${ex.message}")
            } finally {
                dismissLoadingJobWithDelay()
            }
        }
    }

    fun shareHtml(
        context: Context,
        getHtmlBlock: suspend () -> String,
    ) {
        if (isShareTimeInLag()) {
            return
        }
        shareJob?.cancel()
        shareJob = viewModelScope.launch {
            restartShowLoadingJob()
            try {
                val htmlStr = getHtmlBlock.invoke()
                val file = ShareContentUtils.getHtmlFileForShare(htmlStr)
                ShareContentUtils.startHtmlShareIntent(file, context)
            } catch (ex: Exception) {
                Logger.e(TAG, "shareHtml error, ${ex.javaClass}, ${ex.message}")
            } finally {
                dismissLoadingJobWithDelay()
            }
        }
    }

    fun cancelShare() {
        mShareLoadingState.update { false }
        shareJob?.cancel()
    }

    private fun restartShowLoadingJob() {
        showLoadingJob?.cancel()
        showLoadingJob = viewModelScope.launch {
            // 延迟1500显示
            delay(1000)
            mShareLoadingState.update { true }
        }
    }

    private suspend fun dismissLoadingJobWithDelay() {
        // 如果已经显示，那延迟一段时间，避免闪现
        if (mShareLoadingState.value) {
            delay(500)
        }
        mShareLoadingState.update { false }
        showLoadingJob?.cancel()
    }

    private fun isShareTimeInLag(): Boolean {
        val current = System.currentTimeMillis()
        return if (current - lastShareTime < SHARE_LAG) {
            Logger.v(TAG, "isShareTimeInLag true, return")
            true
        } else {
            lastShareTime = current
            false
        }
    }

    /**
     * canUndo状态流
     */
    private val mCanUndoState = MutableStateFlow(false)
    val canUndoState = mCanUndoState.asStateFlow()
    var canUndo by mCanUndoState.delegate()

    /**
     * canRedo状态流
     */
    private val mCanRedoState = MutableStateFlow(false)
    val canRedoState = mCanRedoState.asStateFlow()
    var canRedo by mCanRedoState.delegate()

    /**
     * 撤销
     */
    private val undoFuncDelegate = delegateFunc<Unit>()
    var View.implUndoFunc by undoFuncDelegate
    fun undo() {
        undoFuncDelegate.invokeFunc()
    }

    /**
     * 重做
     */
    private val redoFuncDelegate = delegateFunc<Unit>()
    var View.implRedoFunc by redoFuncDelegate
    fun redo() {
        redoFuncDelegate.invokeFunc()
    }

    companion object {
        private const val TAG = "TextAndDrawViewModel"
        private const val SHARE_LAG = 1500
    }
}