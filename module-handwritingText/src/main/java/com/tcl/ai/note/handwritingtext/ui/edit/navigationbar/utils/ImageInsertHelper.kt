package com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.utils

import android.content.Context
import android.graphics.RectF
import android.net.Uri
import androidx.core.net.toUri
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.repo.NoteRepository2
import com.tcl.ai.note.handwritingtext.utils.ImageUtils
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import java.io.File

object ImageInsertHelper {
    
    /**
     * 插入图片到画板
     */
    fun insertImageToDrawBoard(
        context: Context, 
        uri: Uri, 
        drawRectF: RectF, 
        suniaDrawViewModel: SuniaDrawViewModel,
        richTextViewModel: RichTextViewModel2
    ) {
        val result = ImageUtils.calculateImageInsertRect(context, uri, drawRectF) ?: return
        val (imagePath, showRectF) = result
        
        // 插入到绘图画板
        suniaDrawViewModel.insertBitmap(imagePath, showRectF)
        // 同时更新RichTextViewModel2的图片列表，确保埋点统计正确
        val currentImages = richTextViewModel.uiState.value.images.toMutableList()

        val imageCount = suniaDrawViewModel.imageCountState.value
        if (imageCount == 0) {
            ImageUtils.handleImageUri(context, uri)?.let { uri ->
                Logger.d("insertImageToDrawBoard", "uri: $uri")
                currentImages.add(EditorContent.ImageBlock(uri = uri))
                richTextViewModel.onImagesChanged(currentImages)
//                suniaDrawViewModel.viewModelScope.launchIO {
//                    val noteId = suniaDrawViewModel.noteId
//                    if (noteId != null) {
//                        NoteRepository2.updateFirstPicture(noteId, uri.toString())
//                    }
//                }
            }

        }
    }
}