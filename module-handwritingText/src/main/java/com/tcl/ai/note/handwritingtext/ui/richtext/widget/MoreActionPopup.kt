package com.tcl.ai.note.handwritingtext.ui.richtext.widget

import androidx.compose.foundation.clickable
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.foundation.focusable
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.utils.defShadow
import com.tcl.ai.note.handwritingtext.vm.TextAndDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.HoverMutableInteractionSource
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.handwritingtext.R as HR

/**
 * 更多操作弹框
 * 尺寸：自适应内容宽度
 * 背景：TclTheme.colorScheme.tertiaryBackground，圆角20dp，阴影
 */
@Composable
internal fun MoreActionContent(
    enableFingerDrawing: Boolean,
    onPageSettings: () -> Unit,
    onMoveNote: () -> Unit,
    onShareNote: () -> Unit,
    onDelete: () -> Unit,
    onFingerDrawing: (isOff: Boolean) -> Unit,
    onDismissPopup: () -> Unit, // 新增参数用于子菜单关闭弹框
    richTextViewModel2: RichTextViewModel2 = hiltViewModel(),
    suniaDrawViewModel: SuniaDrawViewModel = hiltViewModel(),
    textAndDrawViewModel: TextAndDrawViewModel = hiltViewModel(),
) {
    val context = LocalContext.current
    var isShareExpanded by remember { mutableStateOf(false) }
    val isDark = isSystemInDarkTheme()


    val scrollState = rememberScrollState()
    Surface(
        modifier = Modifier
            .wrapContentSize()
            .defShadow(20.dp),
        shape = RoundedCornerShape(20.dp),
        color = TclTheme.colorScheme.tertiaryBackground
    ) {
        Column(
            modifier = Modifier
                .width(IntrinsicSize.Max)
                .verticalScroll(scrollState)
                .padding(4.dp)
        ) {
            // Page Settings
            MoreMenuItem(
                text = stringResource(R.string.more_menu_page_settings),
                onClick = {
                    onPageSettings()
                }
            )

            // Move Note
            MoreMenuItem(
                text = stringResource(R.string.more_menu_move_note),
                onClick = {
                    onMoveNote()
                }
            )

            // Share Note (with expand/collapse)
            MoreMenuItemWithArrow(
                text = stringResource(R.string.more_menu_share_note),
                isExpanded = isShareExpanded,
                onClick = {
                    isShareExpanded = !isShareExpanded
                    onShareNote()
                }
            )

            // Share submenu items (when expanded)
            if (isShareExpanded) {
                ShareSubMenuItem(
                    text = stringResource(R.string.action_share_image_file), // 使用已存在的字符串资源
                    onClick = {
                        // ComposableToast.show(context, "分享图片文件")
                        onDismissPopup() // 点击子菜单项后关闭弹框
                        val state = richTextViewModel2.uiState.value
                        textAndDrawViewModel.shareImage(
                            state.content,
                            state.richTextStyleEntity,
                            state.bgMode,
                            state.displayBgColor,
                            isDark,
                            suniaDrawViewModel,
                            context,
                        )
                    }
                )

                ShareSubMenuItem(
                    text = stringResource(R.string.action_share_text_file), // 使用已存在的字符串资源
                    onClick = {
                        // ComposableToast.show(context, "分享文本文件")
                        onDismissPopup() // 点击子菜单项后关闭弹框
                        textAndDrawViewModel.shareHtml(
                            context,
                            richTextViewModel2::toHtml
                        )
                    }
                )

                ShareSubMenuItem(
                    text = stringResource(R.string.action_share_pdf_file), // 使用已存在的字符串资源
                    onClick = {
                        // ComposableToast.show(context, "分享PDF文件")
                        onDismissPopup() // 点击子菜单项后关闭弹框
                        val state = richTextViewModel2.uiState.value
                        textAndDrawViewModel.sharePdf(
                            state.content,
                            state.richTextStyleEntity,
                            state.bgMode,
                            state.displayBgColor,
                            isDark,
                            suniaDrawViewModel,
                            context,
                        )
                    }
                )
            }
            MoreMenuItem(
                text = enableFingerDrawing.judge(
                    R.string.action_share_off_finger_drawing,
                    R.string.action_share_on_finger_drawing

                ).stringRes(),
                onClick = {
                    onFingerDrawing(enableFingerDrawing)
                }
            )

            // Delete
            MoreMenuItem(
                text = stringResource(R.string.delete),
                onClick = {
                    onDelete()
                }
            )
        }
    }
}

@Composable
private fun MoreMenuItem(
    text: String,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(40.dp)
            .clip(RoundedCornerShape(16.dp))
            .focusable()
            .clickable(
                interactionSource = remember { HoverMutableInteractionSource() },
                indication = ripple()
            ) { onClick() },
        contentAlignment = Alignment.CenterStart
    ) {
        Text(
            text = text,
            fontSize = 14.sp,
            fontWeight = FontWeight.Normal,
            fontFamily = FontFamily.Default,
            color = TclTheme.colorScheme.textDialogTitle,
            maxLines = 1,
            modifier = Modifier.padding(horizontal = 16.dp)
        )
    }
}

@Composable
private fun MoreMenuItemWithArrow(
    text: String,
    isExpanded: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(40.dp)
            .clip(RoundedCornerShape(16.dp))
            .focusable()
            .clickable(
                interactionSource = remember { HoverMutableInteractionSource() },
                indication = ripple()
            ) { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = text,
                fontSize = 14.sp,
                fontWeight = FontWeight.Normal,
                fontFamily = FontFamily.Default,
                color = TclTheme.colorScheme.textDialogTitle,
                maxLines = 1
            )

            Spacer(modifier = Modifier.width(0.dp))

            Icon(
                modifier = Modifier.size(20.dp),
                painter = painterResource(
                    id = if (isExpanded) HR.drawable.ic_navigator_collapse else HR.drawable.ic_navigator_expand
                ),
                contentDescription = stringResource(if (isExpanded) R.string.action_collapse else R.string.action_expand),
            )
        }
    }
}

@Composable
private fun ShareSubMenuItem(
    text: String,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(44.dp)
            .clip(RoundedCornerShape(16.dp))
            .focusable()
            .clickable(
                interactionSource = remember { HoverMutableInteractionSource() },
                indication = ripple()
            ) { onClick() },
        contentAlignment = Alignment.CenterStart
    ) {
        Text(
            text = text,
            fontSize = 14.sp,
            fontWeight = FontWeight.Normal,
            fontFamily = FontFamily.Default,
            color = TclTheme.colorScheme.textDialogTitle,
            lineHeight = (14 + 5.2).sp, // fontSize + lineSpacingExtra
            maxLines = 1,
            modifier = Modifier.padding(start = 32.dp, end = 16.dp) // 子菜单左边距更大
        )
    }
} 