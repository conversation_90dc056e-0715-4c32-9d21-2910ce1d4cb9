package com.tcl.ai.note.handwritingtext.ui.edit.meunbar.popup

import android.annotation.SuppressLint
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.widget.EraserSizeSlider
import com.tcl.ai.note.handwritingtext.utils.defShadow
import com.tcl.ai.note.handwritingtext.vm.menu.EraserViewModel
import com.tcl.ai.note.handwritingtext.vm.menu.getEraserRadiusForSdk
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.HoverMutableInteractionSource

enum class EraserMode {
    STROKE, AREA
}

@SuppressLint("DesignSystem")
@Composable
fun EraserToolBar2(
    modifier: Modifier = Modifier,
    eraserViewModel: EraserViewModel,
    onClose: () -> Unit = {},
    onClearAll: () -> Unit = {},
    onEraserSizeChange: (Float, EraserMode) -> Unit = { _, _ -> }
) {
    LaunchedEffect(eraserViewModel.eraserSize, eraserViewModel.currentEraserMode) {
        val radiusPx = eraserViewModel.getEraserRadiusForSdk()
        onEraserSizeChange(radiusPx, eraserViewModel.currentEraserMode)
    }
    val density = LocalDensity.current

    var isDragging by remember { mutableStateOf(false) }
    val shouldShowOverlay = eraserViewModel.selectedMode == 0 && isDragging
    
    // 动画状态
    val strokeModeAnimatedSelection by animateFloatAsState(
        targetValue = if (eraserViewModel.selectedMode == 1) 1f else 0f,
        animationSpec = tween(durationMillis = 200), label = ""
    )
    
    val areaModeAnimatedSelection by animateFloatAsState(
        targetValue = if (eraserViewModel.selectedMode == 0) 1f else 0f,
        animationSpec = tween(durationMillis = 200), label = ""
    )
    val show = eraserViewModel.selectedMode == 0



    Box(
        modifier = modifier.width(328.dp)

    ) {
        // 最低层级：主要内容区域（不包含滑块）
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .defShadow(20.dp)
                .clip(RoundedCornerShape(20.dp))
                .background(color = TclTheme.colorScheme.tertiaryBackground)
            .padding(vertical = 24.dp)

        ) {
            Column {
                // 标题栏 - 去掉关闭按钮
                Text(
                    text = stringResource(R.string.eraser_toolbar_title),
                    fontSize = with(density){20.dp.toSp()},
                    fontWeight = FontWeight.Medium,
                    color = if (shouldShowOverlay) 
                        TclTheme.colorScheme.textDialogTitle.copy(alpha = 0.3f)
                    else 
                        TclTheme.colorScheme.textDialogTitle,
                    fontFamily = FontFamily.Default,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.padding(start = 24.dp, end = 12.dp)
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 橡皮擦模式选择
                Column(modifier = Modifier.padding(start = 13.dp, top = 3.dp, end = 13.dp)) {
                    // Stroke eraser 选项
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clip(RoundedCornerShape(16.dp))
                            .clickable(
                                interactionSource = remember { HoverMutableInteractionSource() },
                                indication = ripple()
                            ) { eraserViewModel.selectMode(1) }
                            .padding(horizontal = 12.dp, vertical = 12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 单选按钮 - 添加动画效果
                        Box(
                            modifier = Modifier
                                .size(20.dp)
                                .padding(start = 1.dp)
                                .drawWithContent {
                                    // 外圆绘制
                                    drawCircle(
                                        color = if (strokeModeAnimatedSelection > 0f) Color(0xFFFF9E00) else Color(0xFFCCCCCC),
                                        radius = 20.dp.toPx() / 2,
                                        style = Stroke(width = 2.dp.toPx())
                                    )
                                    // 内圆动画过渡
                                    if (strokeModeAnimatedSelection > 0f) {
                                        drawCircle(
                                            color = Color(0xFFFF9E00).copy(alpha = strokeModeAnimatedSelection),
                                            radius = 10.dp.toPx() / 2 * strokeModeAnimatedSelection,
                                            center = center
                                        )
                                    }
                                },
                            contentAlignment = Alignment.Center
                        ) { }

                        Spacer(modifier = Modifier.width(10.dp))

                        Text(
                            text = stringResource(R.string.eraser_stroke_mode),
                            fontSize = with(density){14.dp.toSp()},
                            fontWeight = FontWeight.Normal,
                            color = if (shouldShowOverlay) 
                                TclTheme.colorScheme.textDialogTitle.copy(alpha = 0.30f)
                            else 
                                TclTheme.colorScheme.textDialogTitle,
                            fontFamily = FontFamily.Default,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.weight(1f)
                        )
                    }

                    Spacer(modifier = Modifier.height(0.5.dp))

                    // Area eraser 选项 - 去掉图标
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clip(RoundedCornerShape(16.dp))
                            .clickable(
                                interactionSource = remember { HoverMutableInteractionSource() },
                                indication = ripple()
                            ) { eraserViewModel.selectMode(0) }
                            .padding(horizontal = 12.dp, vertical = 12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 单选按钮 - 添加动画效果
                        Box(
                            modifier = Modifier
                                .size(20.dp)
                                .padding(start = 1.dp)
                                .drawWithContent {
                                    // 外圆绘制
                                    drawCircle(
                                        color = if (areaModeAnimatedSelection > 0f) Color(0xFFFF9E00) else Color(0xFFCCCCCC),
                                        radius = 20.dp.toPx() / 2,
                                        style = Stroke(width = 2.dp.toPx())
                                    )
                                    // 内圆动画过渡
                                    if (areaModeAnimatedSelection > 0f) {
                                        drawCircle(
                                            color = Color(0xFFFF9E00).copy(alpha = areaModeAnimatedSelection),
                                            radius = 10.dp.toPx() / 2 * areaModeAnimatedSelection,
                                            center = center
                                        )
                                    }
                                },
                            contentAlignment = Alignment.Center
                        ) { }

                        Spacer(modifier = Modifier.width(10.dp))

                        Text(
                            text = stringResource(R.string.eraser_area_mode),
                            fontSize = with(density){14.dp.toSp()},
                            fontWeight = FontWeight.Normal,
                            color = if (shouldShowOverlay) 
                                TclTheme.colorScheme.textDialogTitle.copy(alpha = 0.30f)
                            else 
                                TclTheme.colorScheme.textDialogTitle,
                            fontFamily = FontFamily.Default,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.weight(1f)
                        )
                    }


                }

                val animatedHeightOffset by animateDpAsState(
                    targetValue = if (show) 48.dp else 0.dp,
                    animationSpec = tween(350),
                    label = "animatedHeightOffset"
                )
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(animatedHeightOffset)
                        .padding(horizontal = 20.dp)
                        .focusable()
                ) {
                    EraserSizeSlider(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(48.dp),
                        eraserSize = eraserViewModel.eraserSize,
                        onSizeChanged = { newSize ->
                            eraserViewModel.updateSize(newSize)
                        },
                        onDragStateChanged = { dragging ->
                            isDragging = dragging
                        }
                    )
                }

                Spacer(modifier = Modifier.height(16.dp).padding(horizontal = 12.dp))

                // Erase all handwriting 按钮 - 纯文本按钮
                Box(
                    modifier = Modifier
                        .padding(horizontal = 12.dp)
                        .fillMaxWidth()
                        .height(44.dp)
                        .clip(RoundedCornerShape(30 .dp))
                        .clickable(
                            interactionSource = remember { HoverMutableInteractionSource() },
                            indication = ripple()
                        ) { onClearAll() },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = stringResource(R.string.eraser_clear_all),
                        color = if (shouldShowOverlay)
                            colorResource(R.color.eraser_clear_all_text).copy(alpha = 0.30f)
                        else
                            colorResource(R.color.eraser_clear_all_text),
                        fontWeight = FontWeight.Medium,
                        fontSize = with(density){16.dp.toSp()},
                        fontFamily = FontFamily.Default,
                        textAlign = TextAlign.Center,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
        }

        // 第二层级：蒙层（完整覆盖）
        if (shouldShowOverlay) {
            Box(
                modifier = Modifier
                    .wrapContentSize()
                    .clip(RoundedCornerShape(20.dp))
                    .background(color = TclTheme.colorScheme.primaryBackground.copy(alpha = 0.3f))
            )

            // 圆形指示器 - Area eraser 显示实际大小 (10-100px)
            val indicatorDiameter = (eraserViewModel.eraserSize).dp

            Box(
                modifier = Modifier
                    .size(indicatorDiameter)
                    .align(Alignment.Center)
                    .offset(y = (-45).dp)
                    .background(
                        color = TclTheme.colorScheme.eraserIndicatorBackground,
                        shape = CircleShape
                    )
                    .border(
                        width = 1.dp,
                        color = TclTheme.colorScheme.eraserIndicatorBorder,
                        shape = CircleShape
                    )
            )
        }






    }
}


