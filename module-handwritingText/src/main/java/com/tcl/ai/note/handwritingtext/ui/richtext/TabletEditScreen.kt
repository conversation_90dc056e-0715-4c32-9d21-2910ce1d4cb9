package com.tcl.ai.note.handwritingtext.ui.richtext

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.OnBackPressedCallback
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.exclude
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.material3.ScaffoldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import androidx.core.app.ActivityCompat.shouldShowRequestPermissionRationale
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import androidx.navigation.compose.currentBackStackEntryAsState
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.bean.BrushMenu
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.Note
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphStyle
import com.tcl.ai.note.handwritingtext.intent.RichTextIntent
import com.tcl.ai.note.handwritingtext.richtext.utils.NoteContentUtil
import com.tcl.ai.note.handwritingtext.state.DrawBoardIntent
import com.tcl.ai.note.handwritingtext.state.RichTextState
import com.tcl.ai.note.handwritingtext.ui.richtext.component.SelectCategory
import com.tcl.ai.note.handwritingtext.ui.richtext.component.removeDecoration
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.CameraPermissionDialog
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.DeleteDataDialog
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.EditScreenTopAppBar
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.PreviewTopAppBar
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.StoragePermissionDialog
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.TabletTopMenuBar
import com.tcl.ai.note.handwritingtext.utils.ImagePickerManager
import com.tcl.ai.note.handwritingtext.utils.ImageUtils.handleImageUri
import com.tcl.ai.note.handwritingtext.vm.ColorSelectorViewModel
import com.tcl.ai.note.handwritingtext.vm.DrawBoardViewModel
import com.tcl.ai.note.handwritingtext.vm.MenuBarViewModel
import com.tcl.ai.note.handwritingtext.vm.RichTextViewModel
import com.tcl.ai.note.handwritingtext.vm.RichTextViewModel.Operation
import com.tcl.ai.note.handwritingtext.vm.ShareContentComponent
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.AppActivityManager
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.ComposableToast
import com.tcl.ai.note.utils.LocalScaffoldPadding
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.voicetotext.util.AudioToTextConstant
import com.tcl.ai.note.voicetotext.util.getAudioDuration
import com.tcl.ai.note.voicetotext.view.widget.TabletTopAudioBlock
import com.tcl.ai.note.voicetotext.vm.AudioToTextViewModel
import com.tcl.ai.note.voicetotext.vm.RecordingViewModel
import com.tcl.ai.note.widget.HorizontalLine
import java.io.File
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.attribute.BasicFileAttributes
import com.tct.theme.core.designsystem.component.TclLoadingDialog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

/**
 * Note新建/编辑页面
 */
@SuppressLint("UnrememberedMutableState", "ContextCastToActivity")
@Composable
fun TabletEditScreen(
    navController: NavController,
    noteId: Long?,
    isLandscape:Boolean,
    viewModel: RichTextViewModel = hiltViewModel(),
    drawBoardViewModel: DrawBoardViewModel = hiltViewModel(),
    recordingViewModel: RecordingViewModel = hiltViewModel(),
    audioToTextViewModel: AudioToTextViewModel = hiltViewModel(),
    colorSelectorViewModel: ColorSelectorViewModel = hiltViewModel(),
    menuBarViewModel: MenuBarViewModel = hiltViewModel(),
    shareContentComponent: ShareContentComponent = remember { ShareContentComponent() },
) {
    val focusManager = LocalFocusManager.current
    // 页面状态
    val state by viewModel.state.collectAsState()
    val audioList by remember(state) {
        mutableStateOf(
            state.contents.filterIsInstance<EditorContent.AudioBlock>()
                .map { it.audioPath }
                .sortedBy { audioPath ->
                    // 按文件创建时间排序，时间早的在前
                    try {
                        val file = File(audioPath)
                        if (file.exists()) {
                            val path: Path = file.toPath()
                            val attr: BasicFileAttributes = Files.readAttributes(path, BasicFileAttributes::class.java)
                            attr.creationTime().toMillis()
                        } else {
                            Long.MAX_VALUE // 文件不存在的放到最后
                        }
                    } catch (e: Exception) {
                        Long.MAX_VALUE // 出错的放到最后
                    }
                }
        )
    }
    var showAudioPanel by remember { mutableStateOf(true) }

    // 返回操作
    var backHandled by remember { mutableStateOf(false) }
    var isReturning by remember { mutableStateOf(false) } // 返回状态标记
    var isNavigatingBack by remember { mutableStateOf(false) } // 导航状态标记

    // 撤销/重做按钮状态
    val canDrawUndo = drawBoardViewModel.canUndo
    val canDrawRedo = drawBoardViewModel.canRedo

    val onDraw = state.bottomMenuType == MenuBar.BRUSH

    // 页面标题内容
    var editTitle by remember { mutableStateOf("") }
    var isShowLoadingDialog by remember { mutableStateOf(false) }

    // 当前分类
    var currentCategoryId by viewModel.currentCategoryId
    var currentCategoryName by viewModel.currentCategoryName
    var currentColorIndex by viewModel.currentColorIndex
    var currentCategoryIcon by viewModel.currentCategoryIcon

    var curNoteId by remember { mutableStateOf(noteId) }
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val lifecycleOwner = LocalLifecycleOwner.current
    val tip = stringResource(R.string.edit_note_saved)


    if (state.note != null && state.note?.title!!.isNotEmpty()) {
        editTitle = state.note!!.title
        curNoteId = state.note!!.noteId
    }

    // 注入分享模块
    state.note?.let { shareContentComponent.injectRecordBitmapComposable(note = it) }

    // 监听导航事件
    val currentBackStack by navController.currentBackStackEntryAsState()
    val currentDestination = currentBackStack?.destination

    val recordPlayingState by audioToTextViewModel.recordPlayingState.collectAsState()

    LaunchedEffect(currentDestination) {
        // 当导航到新页面时激活标记
        if (currentDestination?.route!=null && !currentDestination.route?.contains("edit_screen")!!) { // 排除当前页面
            viewModel.startNavigation()
        }
    }

    // 同步外部数据源
    LaunchedEffect(state.note?.title) {
        state.note?.title?.let { editTitle = it }
    }
    LaunchedEffect(state.note){
        state.note?.let {
            curNoteId = it.noteId
        }
    }

    // 处理平板横屏保存纯标题笔记后返回不显示标题问题，原因是返回事件与协程执行时序问题，且在编辑页返回时会导致协程还未完全执行完就被取消
    LaunchedEffect(state.isSaving) {
        if (!state.isSaving && viewModel.isSaveStart) {
            viewModel.isSaveStart = false
        }
    }

    // AI润色
    LaunchedEffect(state.aiReplaceText){
        if(state.aiReplaceText.isNotEmpty()){
            aIPolishReplace(viewModel,state)
        }
    }

    // 手写转文本
    LaunchedEffect(state.aiInsertText){
        if(state.aiInsertText.isNotEmpty()){
            handwritingToTextReplace(viewModel,state)
        }
    }

    // 预览模式新增分类
    if(state.newCategory!=null){
        currentCategoryId = state.newCategory!!.categoryId.toString()
        currentCategoryName = state.newCategory!!.name
        currentColorIndex = state.newCategory!!.colorIndex.toString()
        currentCategoryIcon = state.newCategory!!.icon.toString()
    }

    // 新建状态
    var isNew = false
    if((curNoteId ==null || curNoteId!! == 0L) && state.contents.isEmpty()){
        isNew = true
    }
    // 如果是新建状态，默认插入一条空的文本内容块

    var topAppBarHeight by remember { mutableIntStateOf(0) }
    // 分类选择弹出
    var showCategoryPopup by remember { mutableStateOf(false) }
    // 是否删除当前Note数据
    var isDeleteDialogShow by remember { mutableStateOf(false) }

    var hasLoaded by remember { mutableStateOf(false) } // 标记是否已加载
    LaunchedEffect(Unit) {
        currentCategoryId = AppDataStore.getData("currentCategoryId", "")
        currentCategoryName = AppDataStore.getData("currentCategoryName", "")
        currentColorIndex = AppDataStore.getData("currentColorIndex", "")
        currentCategoryIcon = AppDataStore.getData("currentCategoryIcon", "")

        // 初始化时加载数据
        if (!hasLoaded && curNoteId != null && curNoteId!! > 0) {
            if(isLandscape){
                viewModel.handleIntent(RichTextIntent.LoadNoteContents(curNoteId!!,true)) // 加载已有数据
                viewModel.handleIntent(RichTextIntent.UpdateFocusedIndex(0))
                viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(0))
            }else{
                viewModel.handleIntent(RichTextIntent.LoadNoteContents(curNoteId!!,false)) // 加载已有数据
            }

            hasLoaded = true // 更新标记，避免重复加载
        }
    }

    val focusRequester = remember { FocusRequester() }
    // 新建状态下添加一条空内容的TextBlock
    LaunchedEffect(isNew) {
        if (isNew && state.contents.isEmpty()) {
            val firstBlock = EditorContent.TextBlock(
                text = TextFieldValue("")
            )
            // 添加内容
            viewModel.handleIntent(RichTextIntent.AddContent(content = firstBlock))
            // 仅在新建且编辑模式时设置焦点
            if (state.editMode) {
                viewModel.handleIntent(RichTextIntent.UpdateFocusedIndex(0))
            }
        }
    }

    // 在页面退出时强制保存和重置历史
    DisposableEffect(Unit) {
        onDispose {
            audioToTextViewModel.stopPlay()
            if (state.canSave && state.editMode) {
                coroutineScope.launch {
                    recordingViewModel.stopRecord()
                    viewModel.handleIntent(RichTextIntent.PerformSave)
                }
            }
            viewModel.handleIntent(RichTextIntent.UpdateMenuType(MenuBar.NONE))
            // 清除撤销重做操作记录
            viewModel.handleIntent(RichTextIntent.ResetHistory)
        }
    }


    // 触发保存（不包含导航）
    fun triggerSave() {
        recordingViewModel.stopRecord()
        viewModel.handleIntent(RichTextIntent.UpdateEditMode(false))
        viewModel.handleIntent(RichTextIntent.PerformSave)
    }

    // 直接执行导航
    fun directNavigateBack() {
        navController.previousBackStackEntry?.savedStateHandle?.set("refresh", true)
        navController.navigateUp()
    }

    LaunchedEffect(state.isSaving) {
        if (!state.isSaving && isReturning) {
            Toast.makeText(context, tip, Toast.LENGTH_SHORT).show()
            isShowLoadingDialog = false

            backHandled = true
            directNavigateBack()
            isReturning = false
        }
    }

    fun canNoteDelete(): Boolean {
        return NoteContentUtil.canNoteDelete(state) { drawBoardViewModel.isStrokesEmpty }
    }

    /**
     * 是否存在有效录音
     */
    fun hasEffectiveAudio():Boolean{
        var hasAudio = false
        state.contents.forEachIndexed { idx, content ->
            if (content is EditorContent.AudioBlock) {
                val audioPath = content.audioPath
                val duration = getAudioDuration(audioPath)
                if (duration > AudioToTextConstant.MIN_RECORD_DURATION) {
                    hasAudio = true
                }
            }
        }
        return hasAudio
    }

    // 统一的后退处理函数
    fun handleBackAction() {
        coroutineScope.launch {
            isNavigatingBack = true
            // 500ms 后显示加载对话框，如果没再保存了则不用管
            val showLoadingJob = launch {
                delay(500)
                if (state.isSaving) {
                    isShowLoadingDialog = true
                }
            }

            // 仅限平板横屏场景，存在编辑页点保存按返回时存在协程丢失情况，在协程最后的保存标识未能得到更新导致不能终止此阻塞
            suspend fun waitSaveStateChange() {
                var isSaveStartTrueCount = 0
                while (viewModel.isSaveStart || state.isSaving) {
                    if (viewModel.isSaveStart) {
                        isSaveStartTrueCount++
                        //最高阻塞450ms就可以执行返回事件，正常300ms左右保存的协程已经能开始执行了
                        if (isSaveStartTrueCount >= 9) {
                            // 在500ms Saving框出现阈值之前可返回
                            break
                        }
                    } else {
                        // 只要一次不是true，重置计数
                        isSaveStartTrueCount = 0
                    }
                    delay(50)
                }
                showLoadingJob.cancel()
            }

            if (state.editMode) {
                state.note?.let {
                    // 如果当前笔记内容为空，则删除当前笔记
                    if (!hasEffectiveAudio() && canNoteDelete()) {
                        viewModel.handleIntent(RichTextIntent.DeleteOneNote(state.note!!.noteId))
                        directNavigateBack()
                    } else {
                        // 触发保存并标记需要返回
                        AppDataStore.putAddNoteId(state.note?.noteId?:0)
                        triggerSave()
                        waitSaveStateChange()
                    }
                }?: run {
                    directNavigateBack()
                }
            } else {
                waitSaveStateChange()
                directNavigateBack()
            }
        }
    }

    // 处理返回键（自动保存 + 导航返回）
    BackHandler(enabled = !backHandled && !state.isSaving) {
        if (!backHandled) {
            backHandled = true
            isReturning = true
            handleBackAction()
        }
    }

    // 监听生命周期变化（处理 Home/任务键）
    var saveJob by remember { mutableStateOf<Job?>(null) }
    DisposableEffect(lifecycleOwner) {
        val activity = context as? ComponentActivity
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_PAUSE -> {
                    val hasFocus = activity?.hasWindowFocus()
                    // 半屏弹窗（仍有焦点）
                    if(hasFocus == true){
                        Logger.d("Lifecycle", "Half screen page open")
                        viewModel.resetNavigation() // 不视为离开
                    }
                    // 应用内导航（通过标记判断）
                    if(state.isNavigatingWithinApp){
                        Logger.d("Lifecycle", "Jump to other pages")
                        viewModel.resetNavigation()
                    }
                }
                Lifecycle.Event.ON_STOP -> {
                    val hasFocus = activity?.hasWindowFocus()
                    if (!isNavigatingBack && state.editMode && state.canSave && activity?.isChangingConfigurations != true && !state.isNavigatingWithinApp) {
                        Logger.d("Lifecycle", "Leave the application and trigger automatic saving")
                        // 立即显示Toast（此时Activity仍可见）
                        if(hasFocus==true){
                            Toast.makeText(context, tip, Toast.LENGTH_SHORT).show()
                        }

                        focusManager.clearFocus()
                        recordingViewModel.stopRecord()
                        viewModel.handleIntent(RichTextIntent.UpdateEditMode(false))

                        // 启动保存操作
                        saveJob = coroutineScope.launch(Dispatchers.IO) {
                            //recordingViewModel.stopRecord()
                            viewModel.handleIntent(RichTextIntent.PerformSave)
                        }
                    }
                    isNavigatingBack = false // 重置导航状态
                    // 重置返回状态
                    isReturning = false
                }
                Lifecycle.Event.ON_RESUME -> {
                    saveJob?.cancel()
                    saveJob = null
                    // 返回当前页面时重置标记
                    viewModel.resetNavigation()
                }
                Lifecycle.Event.ON_DESTROY -> {
                    if (state.editMode) {
                        if (!hasEffectiveAudio() && canNoteDelete()) {
                            viewModel.handleIntent(RichTextIntent.DeleteOneNote(state.note!!.noteId))
                        }
                    }
                }
                else -> {}
            }
        }

        val callback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                backHandled = true
                isReturning = true // 标记返回操作
                handleBackAction()
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)
        activity?.onBackPressedDispatcher?.addCallback(callback)
        onDispose {
            saveJob?.cancel()
            lifecycleOwner.lifecycle.removeObserver(observer)
            callback.remove()
        }
    }

    // 计算是否可以保存
    val canSave by derivedStateOf {
        val hasValidTitle = editTitle.isNotBlank()
        val hasValidContent = state.contents.any { content ->
            when (content) {
                is EditorContent.TextBlock -> content.text.text.isNotBlank()
                is EditorContent.TodoBlock -> content.text.text.isNotBlank()
                is EditorContent.ImageBlock -> true // 只要有图片就算有效内容
                is EditorContent.AudioBlock -> true // 只要有音频就算有效内容
                is EditorContent.RichTextV2 -> false
            }
        }
        hasValidTitle || hasValidContent || !drawBoardViewModel.isStrokesEmpty
    }

    // 更新TopAppBar中的canSave状态
    LaunchedEffect(canSave) {
        viewModel.handleIntent(RichTextIntent.UpdateSaveState(canSave))
    }

    val density = LocalDensity.current.density
    val screenWidthDp = LocalConfiguration.current.screenWidthDp


    // Create the state holder
//    val aiRouteState = rememberAIRouteState(
//        viewModel = viewModel
//    )

    var showStoragePermissionDialog by remember { mutableStateOf(false) }
    var showCameraPermissionDialog by remember { mutableStateOf(false) }

    // 图片插入数量限制
    val maxImageLimit = 50
    val maxImageLimitTip = stringResource(R.string.max_image_limit)
    // 先初始化图片选择启动器（无依赖）
    val pickImagesLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri  ->
        uri?.let {
            // 插入前再次检查数量
            if (state.contents.count { it is EditorContent.ImageBlock } >= maxImageLimit) {
                Toast.makeText(context, maxImageLimitTip, Toast.LENGTH_SHORT).show()
                return@rememberLauncherForActivityResult
            }
            handleImageInsertion(it, context, viewModel, state)
        }
    }

    // 声明拍照启动器（使用 lateinit）
    lateinit var takePhotoLauncher: ActivityResultLauncher<Uri>

    // 初始化管理器（此时只传入 pickImagesLauncher）
    val imagePickerManager = remember {
        ImagePickerManager(
            context = context,
            pickImagesLauncher = pickImagesLauncher
        ).apply {
            // 4. 配置拍照结果处理
            setPhotoCallback { uri ->
                handleImageInsertion(uri, context, viewModel, state)
            }
        }
    }

    val pickSingleMedia = rememberLauncherForActivityResult(
        //设置一个值允许选择照片的，且最多选择9个照片的照片选择器
        ActivityResultContracts.PickVisualMedia()
    ) { uri  ->
        AppActivityManager.isAddingImage.update { false }
        uri?.let {
            // 插入前再次检查数量
            if (state.contents.count { it is EditorContent.ImageBlock } >= maxImageLimit) {
                Toast.makeText(context, maxImageLimitTip, Toast.LENGTH_SHORT).show()
                return@rememberLauncherForActivityResult
            }
            handleImageInsertion(it, context, viewModel, state)
        }
    }

    // 相机权限请求Launcher
    // 处理CAMERA权限请求结果
    val cameraPermissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            // 权限授予后执行拍照
            imagePickerManager.takePhoto()
        } else {
            if (context is ComponentActivity
                && !shouldShowRequestPermissionRationale(context, Manifest.permission.CAMERA)) {
                // 更新状态以显示理由对话框
                showCameraPermissionDialog = true
            }
        }
    }

    // 最后初始化拍照启动器（此时可以安全引用管理器）
    takePhotoLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.TakePicture()
    ) { success ->
        if (success) {
            imagePickerManager.handleTakePhotoResult()
        }
    }

    // 为管理器注入启动器
    imagePickerManager.takePhotoLauncher = takePhotoLauncher


    // 设置回调接口 - 不需要回调的可用 isDrawingMode 判断当前编辑模式
    DisposableEffect(menuBarViewModel) {
        // 设置 RichTextViewModel 引用
        menuBarViewModel.setRichTextViewModel(viewModel, drawBoardViewModel)

        // 设置通用绘图模式操作回调
        menuBarViewModel.onCommonDrawingModeAction = {
            // 执行清除焦点和隐藏drawer的通用操作
            focusManager.clearFocus()
            drawBoardViewModel.goneDrawer()
        }

        // 设置文本编辑模式回调
        menuBarViewModel.onTextEditRequested = {
            /*// 直接从 ViewModel 获取最新状态，而不依赖 Compose 收集的状态
            val isDrawingModeNow = menuBarViewModel.isDrawingMode()
            //val currentMenuTypeNow = menuBarViewModel.getCurrentMenuType()
            // 一期逻辑
            drawBoardViewModel.goneDrawer()
            if (!isDrawingModeNow) {
                viewModel.handleIntent(RichTextIntent.UpdateFocusedIndex(0))
                viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(0))
            }*/
            ComposableToast.show(context, "切换到富文本编辑模式，待关联实际业务")
        }

        // 设置绘图模式回调
        menuBarViewModel.onDrawingEditRequested = { strokeStyle ->
            // 直接从 ViewModel 获取最新状态
            val isDrawingModeNow = menuBarViewModel.isDrawingMode()
            val currentMenuTypeNow = menuBarViewModel.getCurrentMenuType()

            viewModel.handleIntent(RichTextIntent.UpdateMenuType(MenuBar.BRUSH))
            viewModel.handleIntent(RichTextIntent.UpdateBrushMenuType(BrushMenu.PEN))

            // 设置笔刷类型：控件里已经设置了，二期可以考虑替换
            /*drawBoardViewModel.sendIntent(
                DrawBoardIntent.ChangeStrokeStyle(
                    drawBoardViewModel.strokeStyle.copy(
                        doodlePen =  doodlePen,
                        drawMode = DrawMode.PEN  // 一期逻辑
                    )
                )
            )*/
            ComposableToast.show(context, "笔刷: ${strokeStyle.doodlePen} 粗细: ${strokeStyle.width}\n使用的一期参数，待关联实际业务")
        }

        // 设置撤销回调
        menuBarViewModel.onUndoRequested = {
            // 直接从 ViewModel 获取最新状态
            val isDrawingModeNow = menuBarViewModel.isDrawingMode()

            ComposableToast.show(context, "撤销操作，待接入二期...")
        }

        // 设置重做回调
        menuBarViewModel.onRedoRequested = {
            // 直接从 ViewModel 获取最新状态
            val isDrawingModeNow = menuBarViewModel.isDrawingMode()

            ComposableToast.show(context, "重做操作，待接入二期...")
        }

        // 设置颜色选择回调
        menuBarViewModel.onColorSelected = { color ->
            // 直接从 ViewModel 获取最新状态
            //val isDrawingModeNow = menuBarViewModel.isDrawingMode()
            //ComposableToast.show(context, "选择颜色: $color")
        }


        // TODO 待关联编辑历史记录，使撤销/重做按钮可用
        menuBarViewModel.addHistoryState("初始状态", isDrawing = false)
        menuBarViewModel.addHistoryState("修改状态1", isDrawing = false)

        // 清理回调
        onDispose {
            menuBarViewModel.onCommonDrawingModeAction = null
            menuBarViewModel.onTextEditRequested = null
            menuBarViewModel.onDrawingEditRequested = null
            menuBarViewModel.onUndoRequested = null
            menuBarViewModel.onRedoRequested = null
            menuBarViewModel.onColorSelected = null
        }
    }


    Scaffold(
        contentWindowInsets = ScaffoldDefaults
            .contentWindowInsets
            .exclude(WindowInsets.navigationBars),
        containerColor = TclTheme.colorScheme.reWriteExpandBg,
        topBar = {
            Box(
                modifier = Modifier.onGloballyPositioned {
                    topAppBarHeight = it.size.height
                }
            ) {
                Column {
                    if (state.editMode) {
                        // 编辑模式
                        EditScreenTopAppBar(
                            canUndo = if (onDraw) canDrawUndo else state.canUndo,
                            canRedo = if (onDraw) canDrawRedo else state.canRedo,
                            onUndo = {
                                if (onDraw)
                                    drawBoardViewModel.sendIntent(DrawBoardIntent.UndoLast)
                                else{
                                    // 最近一次需要撤销的内容
                                    val content = when(val lastOp = state.undoStack.last()){
                                        is Operation.Add->{
                                            lastOp.content
                                        }

                                        is Operation.Update->{
                                            lastOp.new
                                        }

                                        is Operation.Remove->{
                                            lastOp.content
                                        }

                                        is Operation.ReplaceAll -> {lastOp.newContents[0]}
                                        is Operation.UpdateTitle -> { lastOp.newTitle }
                                        is Operation.Merge -> {lastOp.merged}
                                    }
                                    if(content is EditorContent.AudioBlock){
                                        // 如果最近需要撤销的内容为录音信息并且当前录音正在进行录音，则先停止当前录音再做撤销操作
                                        audioToTextViewModel.stopPlay()
                                        recordingViewModel.stopRecord()
                                        viewModel.handleIntent(RichTextIntent.Undo)
                                    }else{
                                        viewModel.handleIntent(RichTextIntent.Undo)
                                    }

                                }

                            },
                            onRedo = {
                                if (onDraw)
                                    drawBoardViewModel.sendIntent(DrawBoardIntent.RedoLast)
                                else
                                    viewModel.handleIntent(RichTextIntent.Redo)
                            },
                            canSave = canSave,
                            onReturn = {
                                focusManager.clearFocus()
                                // 返回上一页
                                backHandled = true
                                isReturning = true
                                handleBackAction()
                            },
                            onSave = {
                                // 保存
                                if (canSave) {
                                    recordingViewModel.stopRecord()
                                    viewModel.handleIntent(RichTextIntent.RequestSave)
                                    // 页面状态改成预览状态
                                    focusManager.clearFocus()
                                    viewModel.handleIntent(RichTextIntent.UpdateEditMode(false))
                                    viewModel.handleIntent(RichTextIntent.UpdateMenuType(MenuBar.NONE))
                                    viewModel.handleIntent(RichTextIntent.UpdateBrushMenuType(""))
                                    // 清除撤销重做操作记录
                                    viewModel.handleIntent(RichTextIntent.ResetHistory)
                                }
                            },
                            hasAudio = audioList.isNotEmpty(),
                            showAudioPanel = showAudioPanel,
                            onAudioPanelVisibleClick = {
                                showAudioPanel = !showAudioPanel
                            },
                            onAddAudio = { audioPath ->
                                viewModel.handleIntent(
                                    RichTextIntent.AddContent(
                                        index = 0,
                                        content = EditorContent.AudioBlock(audioPath)
                                    ))
                            },
                            recordingViewModel = recordingViewModel
                        )
                    } else {
                        // 预览模式
                        val noContentShareTip = stringResource(R.string.not_content_share)
                        PreviewTopAppBar(
                            showReturn = true,
                            noteId = state.note?.noteId ?:0,
                            currentCategoryIcon = viewModel.currentCategoryIcon.value,
                            currentCategoryColorIndex = viewModel.currentColorIndex.value.toIntOrNull()?:0,
                            showCategoryPopup = showCategoryPopup,
                            isDeleteDialogShow = isDeleteDialogShow,
                            onReturn = {
                                // 返回到列表
                                backHandled = true
                                isReturning = true
                                // 处理平板横屏首页item未能及时显示标题的特殊场景
                                if (!viewModel.isSaveStart) {
                                    viewModel.isSaveStart = true
                                }
                                handleBackAction()
                            },
                            onChangeCategory = { showCategoryPopup = true },
                            onShare = { context, type ->
                                if (type == "Image") {
                                    if (state.note != null) {
                                        if(isOnlyVoice(state.note!!)){
                                            Toast.makeText(context, noContentShareTip, Toast.LENGTH_SHORT).show()
                                        }else{
                                            coroutineScope.launchIO {
                                                shareContentComponent.shareWithImage(context, state.note!!)
                                            }
                                        }
                                    }
                                } else if (type == "Text") {
                                    if (state.note != null) {
                                        if(isOnlyVoice(state.note!!)){
                                            Toast.makeText(context, noContentShareTip, Toast.LENGTH_SHORT).show()
                                        }else{
                                            coroutineScope.launchIO {
                                                shareContentComponent.shareWithHtml(context, state.note!!)
                                            }
                                        }
                                    }
                                } else if (type == "PDF") {
                                    if (state.note != null) {
                                        if(isOnlyVoice(state.note!!)){
                                            Toast.makeText(context, noContentShareTip, Toast.LENGTH_SHORT).show()
                                        }else{
                                            coroutineScope.launchIO {
                                                shareContentComponent.shareWithPdf(context, state.note!!)
                                            }
                                        }
                                    }
                                }

                            },
                            onDelete = {
                                // 删除操作
                                isDeleteDialogShow = true
                            }
                        )
                    }
                    HorizontalLine(
                        thickness = 0.5.dp
                    )

                    TabletTopAudioBlock(
                        noteId = state.note?.noteId ?: 0,
                        audioPaths = audioList,
                        showPanel = showAudioPanel,
                        recordPlayingState = recordPlayingState,
                        onSeek = { position, audioPath ->
                            audioToTextViewModel.seekTo(position, audioPath)
                        },
                        onPlayClick = { audioPath ->
                            audioToTextViewModel.playAudio(audioPath)
                        },
                        onPauseClick = {
                            audioToTextViewModel.pausePlay()
                        },
                        onAudioToTextClick = {
                            viewModel.startNavigation()
                        },
                        onConfirmRename = { oldAudioPath, newAudioPath ->
                            Logger.d("TabletEditScreen", "onConfirmRename: $oldAudioPath -> $newAudioPath")
                            viewModel.handleIntent(
                                RichTextIntent.UpdateAudioPath(
                                    oldAudioPath = oldAudioPath,
                                    newAudioPath = newAudioPath
                                )
                            )
                        },
                        onDeleteAudios = { deletedAudioPaths ->
                            deletedAudioPaths.forEach { deletedAudioPath ->
                                audioToTextViewModel.deleteAudioFile(deletedAudioPath)
                                viewModel.handleIntent(
                                    RichTextIntent.DeleteAudio(deletedAudioPath)
                                )
                            }
                        },
                        onStartRecordingClick = {
                            // 开始录音
                            /*val audioPath = generateAudioPath()
                            recordingViewModel.recordingIntent(RecordIntent.StartRecord(audioPath))
                            viewModel.handleIntent(
                                RichTextIntent.AddContent(
                                    index = 0,
                                    content = EditorContent.AudioBlock(audioPath)
                                ))*/
                        },
                        recordingViewModel = recordingViewModel,
                        audioToTextViewModel = audioToTextViewModel,
                    )
                    // 顶部功能菜单
                    if (state.editMode) {
                        TabletTopMenuBar(
                            viewModel,
                            menuBarViewModel = menuBarViewModel,
                            onBottomClick = {
//                                aiRouteState.handleRoute(state.note,it)
                            },
                            onTodo = onTodo@{ type ->
                                val currentIndex = state.focusedIndex
                                if (currentIndex !in state.contents.indices) return@onTodo

                                when (val currentBlock = state.contents[currentIndex]) {
                                    is EditorContent.TextBlock -> {
                                        // 获取当前文本内容并确保光标位置有效
                                        val textValue = currentBlock.text
                                        val fullText = textValue.text
                                        val cursorPos = state.cursorPosition.coerceIn(0, fullText.length)

                                        // 将文本拆分为多行并确定光标所在行
                                        val lines = fullText.split('\n')
                                        var charCount = 0
                                        var targetLineIndex = -1

                                        // 处理单行和非纯文本情况情况
                                        if (lines.size == 1 || currentBlock.paragraphStyle == ParagraphStyle.BULLETED || currentBlock.paragraphStyle == ParagraphStyle.NUMBERED) {
                                            val validCursor = cursorPos.coerceIn(0, fullText.length)
                                            val newTodoBlock = EditorContent.TodoBlock(
                                                text = textValue.copy(
                                                    selection = TextRange(validCursor)
                                                ),
                                                isDone = false
                                            )
                                            viewModel.handleIntent(
                                                RichTextIntent.UpdateContent(currentIndex, newTodoBlock)
                                            )
                                        } else {
                                            // 多行纯文本转待办事项
                                            // 计算每行的偏移量以识别目标行
                                            val lineOffsets = lines.map { line ->
                                                val start = charCount
                                                charCount += line.length + 1 // 包含换行符的长度计算
                                                start
                                            }

                                            for (i in lines.indices) {
                                                // 包括光标位于换行符的情况
                                                if (cursorPos in lineOffsets[i]..(lineOffsets[i] + lines[i].length)) {
                                                    targetLineIndex = i
                                                    break
                                                }
                                            }

                                            if (targetLineIndex != -1) {
                                                // 计算当前行的真实范围（包含换行符）
                                                val lineStart = lineOffsets[targetLineIndex]
                                                val lineEnd = if (targetLineIndex < lines.lastIndex) {
                                                    lineStart + lines[targetLineIndex].length + 1 // 包含换行符
                                                } else {
                                                    lineStart + lines[targetLineIndex].length     // 最后一行
                                                }

                                                // 分割三部分（保留原有换行结构）
                                                val beforeText = fullText.substring(0, lineStart).let {
                                                    // 移除前一个换行符（如果当前行不是第一行）
                                                    if (targetLineIndex > 0) it.dropLast(1) else it
                                                }
                                                val currentLineText = fullText.substring(lineStart, lineEnd).trimEnd('\n')
                                                val afterText = fullText.substring(lineEnd)

                                                val sanitizedBlocks = mutableListOf<EditorContent>()

                                                // 前部
                                                if (beforeText.isNotEmpty()) {
                                                    sanitizedBlocks.add(
                                                        EditorContent.TextBlock(
                                                            text = TextFieldValue(
                                                                annotatedString = extractAnnotatedRange(
                                                                    currentBlock.text.annotatedString, 0 until beforeText.length
                                                                ),
                                                                selection = TextRange(beforeText.length)
                                                            ),
                                                            paragraphStyle = ParagraphStyle.NONE
                                                        )
                                                    )
                                                }
                                                // 当前行，转待办事项
                                                val lineTextLength = lines[targetLineIndex].length
                                                val todoBlockRange = lineStart until (lineStart + lineTextLength)
                                                sanitizedBlocks.add(
                                                    EditorContent.TodoBlock(
                                                        text = TextFieldValue(
                                                            annotatedString = extractAnnotatedRange(
                                                                currentBlock.text.annotatedString,
                                                                todoBlockRange
                                                            ),
                                                            selection = TextRange((cursorPos - lineStart).coerceIn(0, lineTextLength))
                                                        ),
                                                        isDone = false
                                                    )
                                                )
                                                // 后部
                                                if (afterText.isNotEmpty() && afterText.any { it != '\n' }) {
                                                    sanitizedBlocks.add(
                                                        EditorContent.TextBlock(
                                                            text = TextFieldValue(
                                                                annotatedString = extractAnnotatedRange(
                                                                    currentBlock.text.annotatedString,
                                                                    lineEnd until lineEnd + afterText.length
                                                                ),
                                                                selection = TextRange(0)
                                                            ),
                                                            paragraphStyle = ParagraphStyle.NONE
                                                        )
                                                    )
                                                }

                                                // 替换原有块
                                                viewModel.handleIntent(
                                                    RichTextIntent.ReplaceAllContents(
                                                        state.contents.toMutableList().apply {
                                                            removeAt(currentIndex)
                                                            addAll(currentIndex, sanitizedBlocks)
                                                        }
                                                    )
                                                )

                                                // 更新焦点到新创建的待办事项块
                                                val newFocusIndex = currentIndex + sanitizedBlocks.indexOfFirst { it is EditorContent.TodoBlock }
                                                viewModel.handleIntent(RichTextIntent.UpdateFocusedIndex(newFocusIndex))
                                                viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(lineOffsets[targetLineIndex]))
                                            }
                                        }
                                    }
                                    is EditorContent.TodoBlock -> {
                                        // 转换待办事项为文本块时重置段落样式并移除删除线
                                        val validCursor = state.cursorPosition.coerceIn(0, currentBlock.text.text.length)
                                        val cleanedText = currentBlock.text.annotatedString.let { original ->
                                            buildAnnotatedString {
                                                append(original.text)
                                                val length = original.text.length
                                                original.spanStyles.forEach { span ->
                                                    // 创建去除了删除线的新样式
                                                    val newStyle = span.item.copy(
                                                        textDecoration = span.item.textDecoration
                                                            ?.removeDecoration(TextDecoration.LineThrough)
                                                    )
                                                    val safeStart = span.start.coerceAtLeast(0).coerceAtMost(length)
                                                    val safeEnd = span.end.coerceAtLeast(safeStart).coerceAtMost(length)
                                                    if (newStyle != SpanStyle() && safeStart < safeEnd) {
                                                        addStyle(newStyle, safeStart, safeEnd)
                                                    }
                                                }
                                            }
                                        }
                                        // 转换待办事项为文本块时重置段落样式
                                        val newTextBlock = EditorContent.TextBlock(
                                            text = currentBlock.text.copy(
                                                annotatedString = cleanedText,
                                                selection = TextRange(validCursor)
                                            ),
                                            paragraphStyle = ParagraphStyle.NONE
                                        )
                                        viewModel.handleIntent(
                                            RichTextIntent.UpdateContent(currentIndex, newTextBlock)
                                        )
                                        viewModel.handleIntent(RichTextIntent.UpdateFocusedIndex(currentIndex))
                                    }
                                    else -> {}
                                }
                                // 关闭可能冲突的菜单
                                viewModel.handleIntent(RichTextIntent.UpdateMenuType(MenuBar.NONE))
                            },
                            // 图片选择按钮点击处理
                            onPickImage = {
                                // 获取当前图片数量
                                val imageCount = state.contents.count { it is EditorContent.ImageBlock }
                                if (imageCount >= maxImageLimit) {
                                    Toast.makeText(context, maxImageLimitTip, Toast.LENGTH_SHORT).show()
                                    return@TabletTopMenuBar
                                }
                                AppActivityManager.isAddingImage.update { true }
                                pickSingleMedia.launch(
                                    PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly)
                                )
                            },
                            // 拍照按钮点击处理
                            onTakePhoto = {
                                val imageCount = state.contents.count { it is EditorContent.ImageBlock }
                                if (imageCount >= maxImageLimit) {
                                    Toast.makeText(context, maxImageLimitTip, Toast.LENGTH_SHORT).show()
                                    return@TabletTopMenuBar
                                }
                                AppActivityManager.isTakingPhoto.update { true }
                                cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
                            },
                            onAudioClick = { audioPath, isAdd ->
                                if (isAdd) {
                                    val position = if (state.focusedIndex < 0) state.contents.size - 1 else state.focusedIndex
                                    val item = state.contents[position]

                                    val blockIndex = when {
                                        item is EditorContent.TextBlock -> {
                                            if (item.text.text.isNotEmpty()) {
                                                position + 1
                                            } else {
                                                position
                                            }
                                        }
                                        else -> position + 1
                                    }
                                    // 插入录音
                                    viewModel.handleIntent(RichTextIntent.AddContent(
                                        index = blockIndex,
                                        content = EditorContent.AudioBlock(audioPath)
                                    ))
                                    Logger.d("AudioPath", "blockIndex: $blockIndex, contentSize: ${state.contents.size}")
                                    // 在录音块的下方插入一个空白的EditableTextBlock
                                    if (blockIndex + 1 >= state.contents.size) {
                                        viewModel.handleIntent(RichTextIntent.AddContent(
                                            index = blockIndex + 1,
                                            content = EditorContent.TextBlock(
                                                TextFieldValue("")
                                            )
                                        ))
                                    }
                                }
                            },
                            topAppBarHeight = topAppBarHeight
                        )
                    }
                }

            }
        },
        // 不应在scaffold层添加imePadding，会导致页面完全重组
        // modifier = Modifier.imePadding()
    ) { innerPadding ->
        // Your screen content here
        Box {
            CompositionLocalProvider(
                LocalScaffoldPadding provides innerPadding
            ) {
                ContentContainer(
                    title = if (state.note != null) {
                        state.note!!.title
                    } else {
                        ""
                    },
                    onTitleChange = {
                        editTitle = it
                        viewModel.handleIntent(RichTextIntent.UpdateTitle(it))
                        viewModel.handleIntent(RichTextIntent.RequestSave)
                    },
                    onFocusChanged = {
                        viewModel.handleIntent(RichTextIntent.UpdateTitleFocus(it))
                    },
                    onAddContent = { content, index ->
                        viewModel.handleIntent(RichTextIntent.AddContent(index, content))
                    },
                    onUpdateContent = { index, updatedContent ->
                        viewModel.handleIntent(
                            RichTextIntent.UpdateContent(
                                index,
                                updatedContent
                            )
                        )
                    },
                    onRemoveContent = { index ->
                        viewModel.handleIntent(RichTextIntent.RemoveContent(index))
                    },
                    viewModel = viewModel,
                )
            }

            SelectCategory(
                showPopup = showCategoryPopup,
                onDismiss = { showCategoryPopup = false },
                currentCategoryId = currentCategoryId,
                onSelected = {
                    currentColorIndex = it.colorIndex.toString()
                    currentCategoryId = it.categoryId.toString()
                    currentCategoryIcon = it.icon.toString()
                    if (curNoteId != null && curNoteId!! > 0) {
                        viewModel.handleIntent(
                            RichTextIntent.UpdateNoteCategoryId(
                                curNoteId!!,
                                it.categoryId
                            )
                        )
                    }
                },
                viewModel,
                topAppBarHeight = topAppBarHeight,
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(top = 8.dp, end = 16.dp)
            )
        }
    }

    if (isDeleteDialogShow) {
        DeleteDataDialog(
            text = stringResource(R.string.dialog_title_delete_one_items),
            onDelete = {
                if (curNoteId != null && curNoteId!! > 0) {
                    isDeleteDialogShow = false
                    viewModel.viewModelScope.launchIO {
                        state.contents.forEach { content ->
                            if (content is EditorContent.AudioBlock) {
                                audioToTextViewModel.deleteAudioFile(content.audioPath)
                            }
                        }
                    }
                    viewModel.handleIntent(RichTextIntent.DeleteOneNote(curNoteId!!))
                    // 设置刷新标志
                    navController.previousBackStackEntry?.savedStateHandle?.set("refresh", true)
                    navController.navigateUp()
                }
            },
            onDismiss = {
                isDeleteDialogShow = false
            }
        )
    }

    if(showStoragePermissionDialog){
        StoragePermissionDialog(
            onDismiss = {showStoragePermissionDialog = false},
            onGoToSettings = {
                showStoragePermissionDialog = false
                if (context is ComponentActivity) {
                    toAppSetting(context)
                }
            }
        )
    }

    if(showCameraPermissionDialog){
        CameraPermissionDialog(
            onDismiss = {showCameraPermissionDialog = false},
            onGoToSettings = {
                showCameraPermissionDialog = false
                if (context is ComponentActivity) {
                    toAppSetting(context)
                }
            }
        )
    }

    TclLoadingDialog(
        onDismissRequest = { isShowLoadingDialog = false },
        show = isShowLoadingDialog,
        text = stringResource(R.string.saving),
        properties = DialogProperties(dismissOnBackPress = false, dismissOnClickOutside = false)
    )
}

// 在图片选择器和拍照的回调处理中处理图片插入
private fun handleImageInsertion(uri: Uri, context: Context, viewModel: RichTextViewModel, state: RichTextState) {
    val currentIndex = state.focusedIndex
    val currentBlock = state.contents.getOrNull(currentIndex)
    val newUri = handleImageUri(context, uri) ?: return

    if (currentBlock is EditorContent.TextBlock || currentBlock is EditorContent.TodoBlock) {
        val text = when (currentBlock) {
            is EditorContent.TextBlock -> currentBlock.text.text
            is EditorContent.TodoBlock -> currentBlock.text.text
            else -> ""
        }
        when (val cursorPos = state.cursorPosition.coerceIn(0, text.length)) {
            in 1 until text.length -> {
                // 拆分当前块为前后两部分
                val (beforeBlock, afterBlock) = when (currentBlock) {
                    is EditorContent.TextBlock -> {
                        val beforeAnnotated = currentBlock.text.annotatedString.subSequence(0, cursorPos)
                        val afterAnnotated = currentBlock.text.annotatedString.subSequence(cursorPos, text.length)
                        Pair(
                            currentBlock.copy(text = TextFieldValue(beforeAnnotated, TextRange(cursorPos))),
                            currentBlock.copy(text = TextFieldValue(afterAnnotated, TextRange(0)))
                        )
                    }

                    is EditorContent.TodoBlock -> {
                        val beforeAnnotated = currentBlock.text.annotatedString.subSequence(0, cursorPos)
                        val afterAnnotated = currentBlock.text.annotatedString.subSequence(cursorPos, text.length)
                        Pair(
                            currentBlock.copy(text = TextFieldValue(beforeAnnotated, TextRange(cursorPos))),
                            currentBlock.copy(text = TextFieldValue(afterAnnotated, TextRange(0)))
                        )
                    }

                    else -> Pair(currentBlock, currentBlock)
                }

                // 更新当前块为前部分
                viewModel.handleIntent(RichTextIntent.UpdateContent(currentIndex, beforeBlock))

                // 插入图片块
                viewModel.handleIntent(RichTextIntent.AddContent(currentIndex + 1, EditorContent.ImageBlock(newUri)))

                // 插入后部分块
                viewModel.handleIntent(RichTextIntent.AddContent(currentIndex + 2, afterBlock))

                // 移动焦点到后部分块起始位置
                viewModel.handleIntent(RichTextIntent.UpdateFocusedIndex(currentIndex + 2))
                viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(0))
            }
            0 -> {
                // 光标在开头：插入图片到当前块前面
                viewModel.handleIntent(RichTextIntent.AddContent(currentIndex, EditorContent.ImageBlock(newUri)))
                // 焦点保持到原块（后移一位）
                viewModel.handleIntent(RichTextIntent.UpdateFocusedIndex(currentIndex + 1))
                viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(0))
            }
            else -> { // cursorPos == text.length
                // 在块下方插入图片和空文本
                viewModel.handleIntent(RichTextIntent.AddContent(currentIndex + 1, EditorContent.ImageBlock(newUri)))
                viewModel.handleIntent(RichTextIntent.AddContent(currentIndex + 2, EditorContent.TextBlock(TextFieldValue(""))))
                viewModel.handleIntent(RichTextIntent.UpdateFocusedIndex(currentIndex + 2))
                viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(0))
            }
        }
    } else {
        // 非文本块在块下方插入图片和空文本
        viewModel.handleIntent(RichTextIntent.AddContent(currentIndex + 1, EditorContent.ImageBlock(newUri)))
        viewModel.handleIntent(RichTextIntent.AddContent(currentIndex + 2, EditorContent.TextBlock(TextFieldValue(""))))
        viewModel.handleIntent(RichTextIntent.UpdateFocusedIndex(currentIndex + 2))
        viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(0))
    }

    viewModel.handleIntent(RichTextIntent.RequestSave)
}

/**
 * Note数据是否只有音频
 */
private fun isOnlyVoice(note: Note):Boolean{
    return note.firstPicture.isNullOrEmpty() && note.summary.isNullOrEmpty() && note.hasAudio == true
}

/**
 * 跳转到应用设置界面
 */
private fun toAppSetting(activity: ComponentActivity) {
    val intent = Intent().apply {
        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
        data = Uri.fromParts("package", activity.packageName, null)
    }
    activity.startActivity(intent)
}

/**
 * 将AI润色内容插入到光标处
 */
private fun aIPolishReplace(
    viewModel: RichTextViewModel,
    state: RichTextState,
    delayMillis: Long = 50L
) {
    // 1. 过滤保留图片和语音块
    val preservedContents = state.contents.filter { content ->
        when(content) {
            is EditorContent.ImageBlock,
            is EditorContent.AudioBlock -> true
            else -> false
        }
    }.toMutableList()

    // 2. 创建润色后的文本块
    val newTextBlock = EditorContent.TextBlock(
        text = TextFieldValue(
            text = state.aiReplaceText,
            selection = TextRange(state.aiReplaceText.length)
        )
    )

    // 3. 确定插入位置（插入在保留内容之后）
    val insertPosition = preservedContents.size
    preservedContents.add(newTextBlock)

    // 4. 批量替换内容
    viewModel.handleIntent(RichTextIntent.ReplaceAllContents(preservedContents))

    // 5. 更新焦点状态
    viewModel.handleIntent(RichTextIntent.UpdateFocusedIndex(insertPosition))
    viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(state.aiReplaceText.length))
}

/**
 * 将手写转文本内容插入到光标处
 */
private fun handwritingToTextReplace(
    viewModel: RichTextViewModel,
    state: RichTextState
) {
    var currentIndex = state.focusedIndex
    var cursorPosition = state.cursorPosition
    val titleCursorPosition = state.titleCursorPosition
    if(titleCursorPosition>=0){
        //焦点在标题上
        // 确保光标位置有效
        val validCursor = titleCursorPosition.coerceIn(0, state.title.length)
        val newText = buildString {
            append(state.title.substring(0, validCursor))
            append(state.aiInsertText)
            append(state.title.substring(validCursor))
        }
        // 更新光标
        val newCursorPosition = validCursor + state.aiInsertText.length
        viewModel.handleIntent(RichTextIntent.UpdateTitle(newText))
        viewModel.handleIntent(RichTextIntent.UpdateTitleCursorPosition(newCursorPosition))
        viewModel.handleIntent(RichTextIntent.RequestSave)
    }else{
        // 处理没有焦点的情况
        if (currentIndex == -1) {
            // 寻找最后一个可编辑块（TextBlock 或 TodoBlock）
            val lastEditableIndex = state.contents.indexOfLast { content ->
                content is EditorContent.TextBlock || content is EditorContent.TodoBlock
            }

            currentIndex = if (lastEditableIndex != -1) {
                // 获取最后一个可编辑块的内容长度
                val lastContent = state.contents[lastEditableIndex]
                cursorPosition = when (lastContent) {
                    is EditorContent.TextBlock -> lastContent.text.text.length
                    is EditorContent.TodoBlock -> lastContent.text.text.length
                    else -> 0 // 这个分支理论上不会触发
                }
                lastEditableIndex
            } else {
                // 如果没有可编辑块，创建新的TextBlock并添加到末尾
                val newBlock = EditorContent.TextBlock(text = TextFieldValue(""))
                viewModel.handleIntent(RichTextIntent.AddContent(content = newBlock))
                state.contents.lastIndex.also {
                    cursorPosition = 0 // 新块初始光标位置为0
                }
            }

            // 更新焦点和光标位置
            viewModel.handleIntent(RichTextIntent.UpdateFocusedIndex(currentIndex))
            viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(cursorPosition))
        }

        // 边界检查防止越界
        if (currentIndex !in state.contents.indices) return

        when (val currentBlock = state.contents[currentIndex]) {
            is EditorContent.TextBlock -> {
                // 确保光标位置有效
                val validCursor = cursorPosition.coerceIn(0, currentBlock.text.text.length)

                val newText = buildString {
                    append(currentBlock.text.text.substring(0, validCursor))
                    append(state.aiInsertText)
                    append(currentBlock.text.text.substring(validCursor))
                }
                val updatedBlock = currentBlock.copy(
                    text = TextFieldValue(
                        text = newText,
                        selection = viewModel.safeTextRange(validCursor + state.aiInsertText.length)
                    )
                )
                viewModel.handleIntent(RichTextIntent.UpdateContent(currentIndex, updatedBlock))
                viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(validCursor + state.aiInsertText.length))
            }
            is EditorContent.TodoBlock -> {
                // 确保光标位置有效
                val validCursor = cursorPosition.coerceIn(0, currentBlock.text.text.length)

                val newText = buildString {
                    append(currentBlock.text.text.substring(0, validCursor))
                    append(state.aiInsertText)
                    append(currentBlock.text.text.substring(validCursor))
                }
                val updatedBlock = currentBlock.copy(
                    text = TextFieldValue(
                        text = newText,
                        selection = viewModel.safeTextRange(state.aiInsertText.length)
                    )
                )
                viewModel.handleIntent(RichTextIntent.UpdateContent(currentIndex, updatedBlock))
                viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(validCursor + state.aiInsertText.length))
            }
            is EditorContent.ImageBlock, is EditorContent.AudioBlock -> {
                // 在非文本块后插入新文本块
                val textBlock = EditorContent.TextBlock(
                    text = TextFieldValue(
                        text = state.aiInsertText,
                        selection = TextRange(state.aiInsertText.length)
                    )
                )
                val insertIndex = currentIndex + 1
                viewModel.handleIntent(RichTextIntent.AddContent(insertIndex, textBlock))
                viewModel.handleIntent(RichTextIntent.UpdateFocusedIndex(insertIndex))
                viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(state.aiInsertText.length))
            }

            is EditorContent.RichTextV2 -> {}
        }
    }

    // 重置手写转文本内容
    viewModel.handleIntent(RichTextIntent.ResetHandwritingToText)
}

/**
 * 从 source 中提取 [range] 区间对应的文本和样式，生成新的 AnnotatedString
 * 新的 span start/end 需相对新字符串做偏移
 */
private fun extractAnnotatedRange(source: AnnotatedString, range: IntRange): AnnotatedString {
    val newStr = source.text.substring(range)
    return buildAnnotatedString {
        append(newStr)
        val absStart = range.first
        val absEnd = range.last + 1 // IntRange 是闭区间，substring 是半开区，+1
        source.spanStyles.forEach { span ->
            // 有交集才复制，为准确起见用 max/min 裁剪
            val overlapStart = maxOf(span.start, absStart)
            val overlapEnd = minOf(span.end, absEnd)
            if (overlapStart < overlapEnd) {
                // 变换到新字符串的区间
                val relStart = overlapStart - absStart
                val relEnd = overlapEnd - absStart
                addStyle(span.item, relStart, relEnd)
            }
        }
    }
}