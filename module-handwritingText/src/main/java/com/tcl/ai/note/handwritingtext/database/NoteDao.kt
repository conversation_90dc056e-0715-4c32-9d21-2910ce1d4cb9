package com.tcl.ai.note.handwritingtext.database

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.tcl.ai.note.handwritingtext.database.entity.DBConst
import com.tcl.ai.note.handwritingtext.database.entity.Note
import com.tcl.ai.note.handwritingtext.database.entity.NoteListItem
import kotlinx.coroutines.flow.Flow

@Dao
interface NoteDao {
    @Query("SELECT noteId, title, summary, first_picture, handwriting_thumbnail, hasAudio, categoryId, createTime, modifyTime, bgMode, bgColor FROM notes ORDER BY createTime DESC")
    fun getAllNotes(): Flow<List<NoteListItem>>

    /**
     * 获取所有笔记数据，同时包含分类信息
     */
    @Transaction
    @Query("""
        SELECT n.noteId, n.title,n.content,n.contents, n.summary, n.first_picture, n.handwriting_thumbnail, n.hasAudio, n.categoryId, n.createTime, n.modifyTime, n.bgMode, n.bgColor,
               c.name as categoryName, COALESCE(c.colorIndex, 7) as categoryColorIndex
        FROM notes n
        LEFT JOIN categories c ON n.categoryId = c.categoryId
        ORDER BY n.createTime DESC
    """)
    fun getAllNotesWithCategory(): Flow<List<NoteListItem>>

    @Query("SELECT noteId, title, summary, first_picture,handwriting_thumbnail,hasAudio,categoryId,createTime,modifyTime,bgMode,bgColor FROM notes order by modifyTime DESC")
    fun getAllNotesByModifyTime(): List<NoteListItem>

    // 分页查询
    @Query(
        """
        SELECT noteId, title, summary, first_picture,handwriting_thumbnail,hasAudio,categoryId,createTime,modifyTime,bgMode,bgColor
        FROM notes 
        ORDER BY modifyTime DESC 
        LIMIT :pageSize OFFSET :offset
    """
    )
    suspend fun getAllNotesByModifyTimePaged(offset: Int, pageSize: Int): List<NoteListItem>

    @Query("SELECT COUNT(*) FROM notes WHERE categoryId = :categoryId")
    suspend fun getNotesCountByCategoryId(categoryId: Long): Int

    // 分页查询
    @Query(
        """
        SELECT noteId, title, summary, first_picture,handwriting_thumbnail,hasAudio,categoryId,createTime,modifyTime,bgMode,bgColor
        FROM notes 
        ORDER BY createTime DESC 
        LIMIT :pageSize OFFSET :offset
    """
    )
    suspend fun getAllNotesPaged(offset: Int, pageSize: Int): List<NoteListItem>

    @Query(
        """
        SELECT noteId, title, summary, first_picture,handwriting_thumbnail,hasAudio,categoryId,createTime,modifyTime,bgMode,bgColor
        FROM notes 
        WHERE categoryId = :categoryId 
        ORDER BY createTime DESC 
        LIMIT :pageSize OFFSET :offset
    """
    )
    suspend fun getNotesByCategoryIdPaged(
        categoryId: Long,
        offset: Int,
        pageSize: Int
    ): List<NoteListItem>


    @Query("SELECT noteId, title, summary, first_picture,handwriting_thumbnail,hasAudio,categoryId,createTime, modifyTime, bgMode,bgColor FROM notes where categoryId = :categoryId order by createTime DESC")
    fun getNotesByCategoryId(categoryId: Long): List<NoteListItem>

    @Query("SELECT noteId, title, summary, first_picture,handwriting_thumbnail,hasAudio,categoryId,createTime, modifyTime, bgMode,bgColor FROM notes where categoryId = :categoryId order by modifyTime DESC")
    fun getNotesByCategoryIdByModifyTime(categoryId: Long): List<NoteListItem>

    @Query(
        """
        SELECT noteId, title, summary, first_picture, handwriting_thumbnail,hasAudio,categoryId,createTime,modifyTime,bgMode,bgColor
        FROM notes 
        WHERE categoryId = :categoryId 
        ORDER BY modifyTime DESC 
        LIMIT :pageSize OFFSET :offset
    """
    )
    suspend fun getNotesByCategoryIdByModifyTimePaged(
        categoryId: Long,
        offset: Int,
        pageSize: Int
    ): List<NoteListItem>

    @Query("Delete FROM notes where categoryId = :categoryId")
    fun deleteNotesByCategoryId(categoryId: Long): Int

    @Query("UPDATE ${DBConst.TABLE_NAME_NOTES} SET categoryId = :newCategoryId WHERE categoryId = :oldCategoryId")
    suspend fun updateCategoryId(oldCategoryId: Long, newCategoryId: Long): Int

    @Query("UPDATE ${DBConst.TABLE_NAME_NOTES} SET categoryId = :newCategoryId WHERE noteId IN (:noteIds)")
    suspend fun updateNotesCategoryId(noteIds: List<Long>, newCategoryId: Long): Int

    @Query("UPDATE ${DBConst.TABLE_NAME_NOTES} SET categoryId = :categoryId WHERE noteId =:noteId")
    suspend fun updateNoteCategoryId(noteId: Long, categoryId: Long): Int

    @Insert
    suspend fun insert(note: Note): Long

    @Update
    suspend fun update(note: Note): Int

    @Delete
    suspend fun delete(note: Note): Int

    @Query("DELETE FROM ${DBConst.TABLE_NAME_NOTES} WHERE noteId = :noteId")
    suspend fun deleteOneNote(noteId: Long): Int

    @Query("DELETE FROM ${DBConst.TABLE_NAME_NOTES} WHERE noteId IN (:noteIds)")
    suspend fun deleteNotes(noteIds: List<Long>): Int

    @Query("SELECT * FROM notes WHERE noteId = :noteId")
    suspend fun getNote(noteId: Long): Note?

    // 新增计数查询
    @Query("SELECT COUNT(*) FROM notes")
    suspend fun getNoteCount(): Int

    /**
     * 搜索操作
     */
    @Query("""
    SELECT noteId, title, summary, first_picture, handwriting_thumbnail, hasAudio, categoryId, createTime, modifyTime, bgMode, bgColor 
        FROM ${DBConst.TABLE_NAME_NOTES} 
        WHERE 
            (title LIKE '%' || :query || '%' OR summary LIKE '%' || :query || '%')
            OR
            (
                CASE 
                    WHEN ((first_picture IS NOT NULL OR handwriting_thumbnail IS NOT NULL) AND (summary IS NULL OR summary = '') AND hasAudio = 0) THEN :onlyImage
                    WHEN (hasAudio = 1 AND (summary IS NULL OR summary = '') AND (first_picture IS NULL AND handwriting_thumbnail IS NULL)) THEN :onlyVoice
                    WHEN ((first_picture IS NOT NULL OR handwriting_thumbnail IS NOT NULL) AND hasAudio = 1 AND (summary IS NULL OR summary = '')) THEN :onlyImageVoice
                    ELSE COALESCE(title, '')
                END LIKE '%' || :query || '%'
            )
    """)
    suspend fun searchNotes(query: String,onlyImage:String,onlyVoice:String,onlyImageVoice:String): List<NoteListItem>

    /**
     * 在某个分类里面进行搜索操作
     */
    @Query("""
    SELECT noteId, title, summary, first_picture, handwriting_thumbnail, hasAudio, categoryId, createTime, modifyTime, bgMode, bgColor 
        FROM ${DBConst.TABLE_NAME_NOTES} 
        WHERE 
            categoryId = :categoryId 
            AND (
                (title LIKE '%' || :query || '%' OR summary LIKE '%' || :query || '%')
                OR
                (
                    CASE 
                        WHEN ((first_picture IS NOT NULL OR handwriting_thumbnail IS NOT NULL) AND (summary IS NULL OR summary = '') AND hasAudio = 0) THEN :onlyImage
                        WHEN (hasAudio = 1 AND (summary IS NULL OR summary = '') AND (first_picture IS NULL AND handwriting_thumbnail IS NULL)) THEN :onlyVoice
                        WHEN ((first_picture IS NOT NULL OR handwriting_thumbnail IS NOT NULL) AND hasAudio = 1 AND (summary IS NULL OR summary = '')) THEN :onlyImageVoice
                        ELSE COALESCE(title, '')
                    END LIKE '%' || :query || '%'
                )
            )
    """)
    suspend fun searchNotesByCategoryAndQuery(query: String, categoryId: Long,onlyImage:String,onlyVoice:String,onlyImageVoice:String): List<NoteListItem>

    @Query("SELECT COUNT(*) FROM notes")
     fun getNoteCountFlow(): Flow<Int>

    @Query("UPDATE ${DBConst.TABLE_NAME_NOTES} SET handwriting_thumbnail = :bitmapPath, modifyTime = :modifyTime WHERE noteId =:noteId")
    suspend fun updateThumbnail(noteId: Long, bitmapPath: String, modifyTime: Long): Int

    @Query("UPDATE ${DBConst.TABLE_NAME_NOTES} SET first_picture = :picturePath WHERE noteId =:noteId")
    suspend fun updateFirstPicture(noteId: Long, picturePath: String): Int
}