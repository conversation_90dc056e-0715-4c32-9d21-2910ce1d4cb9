package com.tcl.ai.note.utils

import androidx.compose.foundation.interaction.HoverInteraction
import androidx.compose.foundation.interaction.Interaction
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.PressInteraction
import com.tcl.ai.note.GlobalContext.Companion.applicationScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch


fun HoverMutableInteractionSource(): MutableInteractionSource = HoverCancelInteractionSourceImpl()

class HoverCancelInteractionSourceImpl : MutableInteractionSource {
    private var lastEnter: HoverInteraction.Enter? = null
    private var clearJob: Job? = null

    override val interactions = MutableSharedFlow<Interaction>(
        extraBufferCapacity = 16,
        onBufferOverflow = BufferOverflow.DROP_OLDEST,
    )

    override suspend fun emit(interaction: Interaction) {
        when (interaction) {
            is HoverInteraction.Enter -> {
                lastEnter = interaction
                startClearTimer()
            }
            is HoverInteraction.Exit -> {
                lastEnter = null
            }
            is PressInteraction.Press,is PressInteraction.Release ,is PressInteraction.Cancel -> {
                lastEnter?.let {
                    interactions.emit(HoverInteraction.Exit(it))
                }
                lastEnter = null
            }
        }
        interactions.emit(interaction)
    }

    override fun tryEmit(interaction: Interaction): Boolean {
        when (interaction) {
            is HoverInteraction.Enter -> {
                lastEnter = interaction
                startClearTimer()
            }
            is HoverInteraction.Exit -> {
                lastEnter = null
            }
            is PressInteraction.Press,is PressInteraction.Release ,is PressInteraction.Cancel ->{
                lastEnter?.let {
                    interactions.tryEmit(HoverInteraction.Exit(it))
                }
                lastEnter = null
            }
        }
        return interactions.tryEmit(interaction)
    }

    private fun startClearTimer() {
        cancelClearTimer()
        clearJob = applicationScope.launch {
            delay(100)
            lastEnter?.let {
                lastEnter?.let {
                    interactions.emit(HoverInteraction.Exit(it))
                }
                lastEnter = null
            }
        }
    }

    private fun cancelClearTimer() {
        clearJob?.cancel()
        clearJob = null
    }
}