package com.sunia.viewlib.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.RectF;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.widget.Toast;
import androidx.annotation.Nullable;
import com.sunia.penengine.sdk.data.IAttachment;
import com.sunia.penengine.sdk.data.RecoInfoRectF;
import com.sunia.penengine.sdk.operate.edit.PasteInfo;
import com.sunia.penengine.sdk.operate.edit.SelectRectF;
import com.sunia.penengine.sdk.operate.edit.ShapeFlag;
import com.sunia.singlepage.sdk.InkFunc;
import com.sunia.singlepage.sdk.listener.IInkEditListener;
import com.sunia.singlepage.sdk.param.LayoutMode;
import com.sunia.singlepage.sdk.param.SelectActionType;
import com.sunia.viewlib.model.EraserViewMode;
import com.sunia.viewlib.model.SelectViewModel;
import com.sunia.viewlib.model.VerticalBlankBg;
import org.jetbrains.annotations.NotNull;

import java.util.List;


public class EditDrawView extends View implements IInkEditListener {
    public static final int CUSTOM_CLICK_DELETE = 1;
    private Context context;
    private SelectViewModel selectViewModel;
    private EraserViewMode eraserViewMode;
    private VerticalBlankBg verticalBlankBg;
    private EditDrawListener listener;
    private Handler mainHandler;
    private RectF visibleRectF = new RectF();
    private LayoutMode layoutMode;

    public EditDrawView(Context context, boolean isTablet) {
        this(context, null, isTablet);
    }

    public EditDrawView(Context context, @Nullable AttributeSet attrs, boolean isTablet) {
        this(context, attrs, 0, isTablet);
    }

    public EditDrawView(Context context, @Nullable AttributeSet attrs, int defStyleAttr, boolean isTablet) {
        super(context, attrs, defStyleAttr);
        init(context, isTablet);
    }

    private void init(Context context, boolean isTablet) {
        this.context = context;
        eraserViewMode = new EraserViewMode();
        selectViewModel = new SelectViewModel(getContext(), isTablet);
        verticalBlankBg = new VerticalBlankBg();
        mainHandler = new Handler(Looper.getMainLooper());
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        visibleRectF.set(0f, 0f, w, h);
        if (selectViewModel != null) {
            selectViewModel.setVisibleRectF(visibleRectF);
        }
        if (verticalBlankBg != null) {
            verticalBlankBg.setVisibleRectF(visibleRectF);
        }
    }

    public void setListener(EditDrawListener listener) {
        this.listener = listener;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        canvas.save();
        canvas.clipRect(visibleRectF);
        if (eraserViewMode != null) {
            eraserViewMode.onDraw(canvas);
        }
        if (selectViewModel != null) {
            selectViewModel.onDraw(context, canvas);
        }
        canvas.restore();
        if(verticalBlankBg != null){
            verticalBlankBg.onDraw(canvas);
        }
    }

    @Override
    public void onEraserDraw(float[] data, boolean isCircle) {
        eraserViewMode.onEraserDraw(data, isCircle);
        refreshView();
    }

    @Override
    public void onInsertShapeDraw(Path path) {
        selectViewModel.onInsertShapeDraw(path);
        refreshView();
    }

    @Override
    public void onSelectPathDraw(Path path) {
        selectViewModel.onSelectPathDraw(path);
        refreshView();
    }

    @Override
    public boolean isDownOnScaleRect(float eventX, float eventY) {
        return selectViewModel.isDownOnScaleRect(eventX, eventY);
    }

    @Override
    public boolean isDownOnRotateRect(float eventX, float eventY) {
        return selectViewModel.isDownOnRotateRect(eventX, eventY);
    }

    @Override
    public boolean isDownOnMixedRect(float eventX, float eventY) {
        return selectViewModel.isDownOnMixedRect(eventX, eventY);
    }

    @Override
    public boolean isDownOnSpannedLeft(float eventX, float eventY) {
        return selectViewModel.isDownOnSpannedLeft(eventX, eventY);
    }

    @Override
    public boolean isDownOnSpannedRight(float eventX, float eventY) {
        return selectViewModel.isDownOnSpannedRight(eventX, eventY);
    }

    @Override
    public boolean isDownOnCustomClick(float eventX, float eventY) {
        return selectViewModel.isDownOnCustomClick(eventX, eventY);
    }

    @Override
    public void onCustomClick(float eventX, float eventY) {

    }

    public int getCustomClickType() {
        return selectViewModel.getCustomClickType();
    }

    @Override
    public void onSelectedDraw(int selectType, SelectRectF selectRectF, RectF visibleRectF, boolean showSelectMenu) {
        if (visibleRectF != null && !visibleRectF.isEmpty()) {
            this.visibleRectF.set(visibleRectF);
        }
        if (selectType == SelectActionType.SELECT_TYPE_ATTACHMENT) {

        }else {
            mainHandler.post(new Runnable() {
                @Override
                public void run() {
                    if (selectType != SelectActionType.SELECT_TYPE_RECO_RECTIFY && selectType != SelectActionType.SELECT_TYPE_RECO_FORMULA) {
                        selectViewModel.onSelectedDraw(selectType, selectRectF);
                    }
                }
            });
        }
        postInvalidate();
        if (showSelectMenu || selectType == SelectActionType.SELECT_TYPE_RECO_RECTIFY || selectType == SelectActionType.SELECT_TYPE_RECO_FORMULA) {
            mainHandler.post(new Runnable() {
                @Override
                public void run() {
                    listener.showSelectMenu(selectType, selectRectF);
                }
            });
        }
    }

//    @Override
//    public void onSelectEditDraw(int selectType, Path path, SelectRectF selectRectF, PointF centerPoint) {
//        selectViewModel.onSelectEditDraw(selectType, path, selectRectF, centerPoint);
//        refreshView();
//    }

    @Override
    public void onSelectEditDraw(int selectType, Path path, SelectRectF selectRectF, RectF visibleRectF, PointF centerPoint) {
        if (visibleRectF != null && !visibleRectF.isEmpty()) {
            this.visibleRectF.set(visibleRectF);
        }
        selectViewModel.onSelectEditDraw(selectType, path, selectRectF, centerPoint);
        refreshView();
    }

    @Override
    public void showTableEditWindow(RectF selectCellRectF, RectF tableRectF, int rowCount, int columnCount) {
        if (null == selectCellRectF || selectCellRectF.isEmpty() || null == tableRectF || tableRectF.isEmpty()) {
            return;
        }
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                listener.showTableEditMenu(selectCellRectF, tableRectF, rowCount, columnCount);
            }
        });
    }

    @Override
    public void onVertexShapePoint(List<PointF> pointList, SelectRectF selectRectF) {
        selectViewModel.onVertexShapePoint(pointList,selectRectF);
    }

    @Override
    public void onOutLineDraw(Path path) {
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                selectViewModel.onOutLineDraw(path);
                invalidate();
            }
        });
    }

    @Override
    public void showPasteWindow(int type, float x, float y) {
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                listener.showPasteMenu(type, x, y);
            }
        });
    }

    @Override
    public void onSelectLimited(int selectLimitType) {
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                String msg = "";
//                if (selectLimitType == SelectLimitType.MAX_AREA_HEIGHT.value) {
//                    msg = context.getString(R.string.limit_max_height);
//                } else if (selectLimitType == SelectLimitType.MAX_AREA_WIDTH.value) {
//                    msg = context.getString(R.string.limit_max_width);
//                } else if (selectLimitType == SelectLimitType.MIN_AREA_WIDTH.value) {
//                    msg = context.getString(R.string.limit_min_width);
//                } else if (selectLimitType == SelectLimitType.MIN_AREA_HEIGHT.value) {
//                    msg = context.getString(R.string.limit_min_height);
//                } else if (selectLimitType == SelectLimitType.MAX_PEN_SIZE.value) {
//                    msg = context.getString(R.string.limit_pen_size_max);
//                } else if (selectLimitType == SelectLimitType.MIN_PEN_SIZE.value) {
//                    msg = context.getString(R.string.limit_pen_size_min);
//                }
                if (!TextUtils.isEmpty(msg)) {
                    Toast.makeText(context, msg, Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    private long lastSelectHintTime;
    @Override
    public void onSelectingHint() {
        if (System.currentTimeMillis() - lastSelectHintTime < 1500) {
            return;
        }
        lastSelectHintTime = System.currentTimeMillis();
    }

    @Override
    public void onDataCopied(PasteInfo pasteInfo) {

    }

    @Override
    public void onClickBlankArea(float eventX, float eventY) {

    }

    @Override
    public void onRotate(float angle, float revisedAngle) {

    }

    @Override
    public void checkSpannedEdit(SelectRectF selectRectF) {
        // 多实例场景会回调, 无需实现
    }

    @Override
    public void reset() {
        visibleRectF.set(0f,0f, getWidth(), getHeight());
        if (eraserViewMode != null) {
            eraserViewMode.reset();
        }
        mainHandler.post(()->{
            if (selectViewModel != null) {
                selectViewModel.reset();
            }
        });
        postInvalidate();
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                if(listener!=null){
                    listener.reset();
                }
            }
        });
    }

    @Override
    public void finishSpannedEdit() {

    }

    @Override
    public ShapeFlag getShapeFlagByShapeId(int id) {
        return ShapeFlag.SHAPE_FLAG_STROKE;
    }

    @Override
    public void onCanvasHandleBusy() {
        Toast.makeText(context, "on Canvas Busy", Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onAutoSelectByEngine(int recoDataType) {
        Log.d("EditDrawView", "onAutoSelectByEngine: $recoDataType");
    }

    @Override
    public void onCurveClicked(long seekTime) {

    }

    @Override
    public void onCancelAutoPlay() {

    }

    private void refreshView() {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            invalidate();
        } else {
            postInvalidate();
        }
    }

    public void setSearchPath(List<Path> searchPath) {
        mainHandler.post(()->{
            selectViewModel.setSearchPath(searchPath);
            selectViewModel.setScaleOffset(getWidth() / 1000f, 1f, 0, 0);
            invalidate();
        });
    }

    public void setRecoInfoRect(RecoInfoRectF[] recoInfoRects) {
        selectViewModel.setRecoInfoRect(recoInfoRects);
        invalidate();
    }

    public void setScaleOffset(float ratio, float scale, float offsetX, float offsetY) {
        mainHandler.post(()->{
            eraserViewMode.setScaleOffset(ratio, scale, offsetX, offsetY);
            selectViewModel.setScaleOffset(ratio, scale, offsetX, offsetY);
            verticalBlankBg.setScaleOffset(ratio, scale, offsetX, offsetY);
            eraserViewMode.setScale(scale);
            invalidate();
        });

    }

    public void setInkFunc(@NotNull InkFunc inkFunc) {
        if (selectViewModel != null) {
            selectViewModel.setInkFunc(inkFunc);
        }
    }

    public void setLayoutMode(LayoutMode layoutMode) {
        this.layoutMode = layoutMode;
        if (verticalBlankBg != null) {
            verticalBlankBg.setLayoutMode(layoutMode);
        }
    }

    public interface EditDrawListener {
        void showSelectMenu(int selectType, SelectRectF selectRectF);

        void showTableEditMenu(RectF selectCellRectF, RectF tableRectF, int rowCount, int columnCount);

        void showPasteMenu(int type, float x, float y);

        void reset();
    }

    public void clearHighLight(){
        mainHandler.post(()->{
            if(selectViewModel != null ){
                selectViewModel.clearHighLight();
                invalidate();
            }
        });
    }

    public void setdrawSavePaths(List<Path> savePaths) {
        selectViewModel.setdrawSavePaths(savePaths);
    }
}
