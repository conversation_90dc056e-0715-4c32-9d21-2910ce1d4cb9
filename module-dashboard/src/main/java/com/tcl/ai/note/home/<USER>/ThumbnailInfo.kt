package com.tcl.ai.note.home.model

/**
 * 缩略图相关信息
 */
data class ThumbnailInfo(
    val type: ThumbnailType = ThumbnailType.TEXT_ICON, // 缩略图显示类型
    val showAudioIcon: Boolean = false, // 是否显示音频图标
    val image: String? = null, // 图片路径
    val handwritingThumbnail: String? = null, // 手绘缩略图
    val handwritingThumbnailDark: String? = null, // 手绘缩略图 深色模式
    val firstPicture: String? = null, // 首图
    val hasAudio: Boolean = false // 是否有录音
)

/**
 * 缩略图显示类型
 */
enum class ThumbnailType {
    PURE_TEXT,    // 纯文本，显示正文内容
    FIRST_SCREEN,    // 显示第一屏信息（图片/手绘）
    AUDIO_ICON,      // 显示录音图标
    TEXT_ICON,       // 显示文本图标
    MIXED_CONTENT,    // 混合模式：底部富文本，上层叠加手绘/图片
    UNKNOWN // 未知类型
}