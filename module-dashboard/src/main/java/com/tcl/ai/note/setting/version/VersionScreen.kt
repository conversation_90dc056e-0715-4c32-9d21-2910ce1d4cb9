package com.tcl.ai.note.setting.version

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.base.R
import com.tcl.ai.note.home.components.HomeCategoryAddCategoryTextStyle
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.isTabletLandscape
import com.tcl.ai.note.widget.AppTopBar
import com.tcl.ai.note.widget.clickableHover
import com.tct.theme.core.designsystem.component.TclLoadingIndicator
import kotlinx.coroutines.launch
import java.util.LinkedList


@Composable
fun VersionRoute(
    onBackClick: () -> Unit,
    viewModel: VerisonViewModel = hiltViewModel(),
) {
    val updateState by viewModel.updateState.collectAsStateWithLifecycle()
    val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()
    val isOffline by viewModel.isOffline.collectAsStateWithLifecycle()
    val context = LocalContext.current
    DisposableEffect(key1 = viewModel.effect) {
        val job = viewModel.viewModelScope.launch {
            viewModel.effect.collect {
                Logger.d("versionroute", it.toString())
                when (it) {
                    VerisonEffect.ShowAota -> {
                        showAota(context)
                    }
                    is VerisonEffect.ShowToastRes -> {
                        ToastUtils.makeWithCancel(it.resourceId)
                    }
                }
            }
        }
        onDispose { job.cancel() }
    }

    DisposableEffect(key1 = viewModel.isOffline) {
        val job = viewModel.viewModelScope.launch {
            viewModel.isOffline.collect { offline ->
                if (offline) {
                    ToastUtils.makeWithCancel(com.tcl.ai.note.base.R.string.network_error)
                    viewModel._isLoading.value = false
                } else {
                    viewModel.onUpdateClick( false)
                }
            }
        }
        onDispose { job.cancel() }
    }

    VersionScreen(
        onBackClick = onBackClick,
        onUpdateClick = {
            if (isOffline) {
                ToastUtils.makeWithCancel(R.string.network_error)
            } else {
                showAota(context)
            }
        },
        version = getVersionName(context) ?: "V1.0.000.12345",
        isLoading = isLoading,
        isOffline = isOffline,
        updateState = updateState
    )

}

fun showAota(context: Context) {
    try {
        val intent = Intent("com.tcl.ota.action.VIEW_APP")
        intent.setPackage("com.tct.smart.aota")
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent)
    } catch (e: ActivityNotFoundException) {
        ToastUtils.makeWithCancel(R.string.aota_not_exist)
    }
}

fun getVersionName(context: Context): String? {
    val manager = context.packageManager
    var versionName: String? = null
    try {
        val info = manager.getPackageInfo(context.packageName, 0)
        versionName = info.versionName
    } catch (e: PackageManager.NameNotFoundException) {
        e.printStackTrace()
    }
    return versionName
}


@Composable
internal fun VersionScreen(
    onBackClick: () -> Unit,
    onUpdateClick: () -> Unit,
    version: String,
    isLoading: Boolean,
    isOffline: Boolean,
    updateState: UpdateMonitorImpl.UpdateState
) {
    val clickTimeStamps = remember {
        LinkedList<Long>()
    }

    Column(
        modifier = Modifier
            .background(color = colorResource(R.color.home_note_list_bg_color))
            .fillMaxSize()
    ) {
        AppTopBar(
            title = "",
            onBackClick = onBackClick,
        )
        if (isTablet) {
            if (isTabletLandscape) {
                TabletLandscapeView(modifier = Modifier.fillMaxWidth(), version, isOffline, updateState, onUpdateClick, clickTimeStamps)
            } else {
                TabletPortraitView(modifier = Modifier.fillMaxWidth(), version, isOffline, updateState, onUpdateClick, clickTimeStamps)
            }
        } else {
            phoneView(modifier = Modifier.fillMaxWidth(), version, isOffline, updateState, onUpdateClick, clickTimeStamps)
        }

        val bottomDp =   if (isTablet) {
            if (isTabletLandscape) {
                152.dp
            }else{
                237.dp
            }
        } else {
            177.dp
        }

        if (isLoading) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(bottom = bottomDp),
                verticalArrangement = Arrangement.Bottom,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                TclLoadingIndicator(
                    color = TclTheme.colorScheme.loadingIndicatorColor,
                    size = 48.dp,
                )
            }
        }
    }
}

@Composable
private fun TabletLandscapeView(
    modifier: Modifier,
    version: String,
    isOffline: Boolean,
    updateState: UpdateMonitorImpl.UpdateState,
    onUpdateClick: () -> Unit,
    clickTimeStamps: LinkedList<Long>
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Box(
            modifier = Modifier.padding(top = 200.dp).size(80.dp).clip(RoundedCornerShape(24.dp))
        ) {
            Image(
                painter = painterResource(id = R.drawable.note_logo),
                contentDescription = null,
                modifier = Modifier.size(80.dp),
            )
        }

        Text(
            text = stringResource(R.string.app_name),
            modifier = Modifier.padding(top = 10.dp),
            style = TextStyle(
                fontFamily = FontFamily.SansSerif,
                fontSize = 20.sp,
                fontWeight = FontWeight.W500,
                color = if (isSystemInDarkTheme()) Color(0xe6FFFFFF) else Color(0xe6000000)
            )
        )

        Text(
            text = version,
            style = TextStyle(
                fontFamily = FontFamily.SansSerif,
                fontWeight = FontWeight.Normal,
                fontSize = 12.sp,
                color = if (isSystemInDarkTheme()) Color(0x80FFFFFF) else Color(0x80000000)
            ),
            modifier = Modifier
                .padding(top = 8.dp)
                .pointerInput(Unit) {
                    detectTapGestures(
                        onTap = {
                            val currentTime = System.currentTimeMillis()
                            clickTimeStamps.add(currentTime)
                            while (clickTimeStamps.isNotEmpty()
                                && currentTime - clickTimeStamps.first > ENABLE_LOG_TIME_WINDOW
                            ) {
                                clickTimeStamps.removeFirst()
                            }
                            if (clickTimeStamps.size >= ENABLE_LOG_CLICK_TIME) {
                                clickTimeStamps.clear()
                                val isLogEnabled = !Logger.isLogEnabled
                                Logger.isLogEnabled = isLogEnabled
                                ToastUtils.makeWithCancel(
                                    if (isLogEnabled) {
                                        R.string.enable_log
                                    } else {
                                        R.string.disable_log
                                    }
                                )
                            }
                        }
                    )
                }
        )

        if (isOffline) {
            Text(
                text = stringResource(R.string.cannot_check_updates),
                modifier = Modifier.padding(top = 5.dp),
                style = TextStyle(
                    fontFamily = FontFamily.SansSerif,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Normal,
                    color = if (isSystemInDarkTheme()) Color(0x80FFFFFF) else Color(0x80000000)
                )
            )
        }

        if (updateState == UpdateMonitorImpl.UpdateState.LatestVersion && !isOffline) {
            Text(
                text = stringResource(R.string.lastest_version_tips),
                modifier = Modifier.padding(top = 4.dp),
                style = TextStyle(
                    fontFamily = FontFamily.SansSerif,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Normal,
                    color = if (isSystemInDarkTheme()) Color(0x80FFFFFF) else Color(0x80000000)
                )
            )
        } else {
            var updateText = ""
            if (isOffline) {
                updateText = stringResource(R.string.text_try_again)
            } else {
                updateText = stringResource(R.string.updates)
            }

            if (updateState == UpdateMonitorImpl.UpdateState.NewVersionAvailable || isOffline) {
                Spacer(modifier = Modifier.weight(1f))
                updateBottom(300, 56, updateText, onUpdateClick)
            }
        }
    }
}

@Composable
private fun TabletPortraitView(
    modifier: Modifier,
    version: String,
    isOffline: Boolean,
    updateState: UpdateMonitorImpl.UpdateState,
    onUpdateClick: () -> Unit,
    clickTimeStamps: LinkedList<Long>
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Box(
            modifier = Modifier.padding(top = 390.dp).size(80.dp).clip(RoundedCornerShape(24.dp))
        ) {
            Image(
                painter = painterResource(id = R.drawable.note_logo),
                contentDescription = null,
                modifier = Modifier.size(80.dp),
            )
        }

        Text(
            text = stringResource(R.string.app_name),
            modifier = Modifier.padding(top = 10.dp),
            style = TextStyle(
                fontFamily = FontFamily.SansSerif,
                fontSize = 20.sp,
                fontWeight = FontWeight.W500,
                color = if (isSystemInDarkTheme()) Color(0xe6FFFFFF) else Color(0xe6000000)
            )
        )

        Text(
            text = version,
            style = TextStyle(
                fontFamily = FontFamily.SansSerif,
                fontWeight = FontWeight.W400,
                fontSize = 12.sp,
                color = if (isSystemInDarkTheme()) Color(0x80FFFFFF) else Color(0x80000000)
            ),
            modifier = Modifier
                .padding(top = 8.dp)
                .pointerInput(Unit) {
                    detectTapGestures(
                        onTap = {
                            val currentTime = System.currentTimeMillis()
                            clickTimeStamps.add(currentTime)
                            while (clickTimeStamps.isNotEmpty()
                                && currentTime - clickTimeStamps.first > ENABLE_LOG_TIME_WINDOW
                            ) {
                                clickTimeStamps.removeFirst()
                            }
                            if (clickTimeStamps.size >= ENABLE_LOG_CLICK_TIME) {
                                clickTimeStamps.clear()
                                val isLogEnabled = !Logger.isLogEnabled
                                Logger.isLogEnabled = isLogEnabled
                                ToastUtils.makeWithCancel(
                                    if (isLogEnabled) {
                                        R.string.enable_log
                                    } else {
                                        R.string.disable_log
                                    }
                                )
                            }
                        }
                    )
                }
        )

        if (isOffline) {
            Text(
                text = stringResource(R.string.cannot_check_updates),
                modifier = Modifier.padding(top = 5.dp),
                style = TextStyle(
                    fontFamily = FontFamily.SansSerif,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Normal,
                    color = if (isSystemInDarkTheme()) Color(0x80FFFFFF) else Color(0x80000000)
                )
            )
        }

        if (updateState == UpdateMonitorImpl.UpdateState.LatestVersion && !isOffline) {
            Text(
                text = stringResource(R.string.lastest_version_tips),
                modifier = Modifier.padding(top = 4.dp),
                style = TextStyle(
                    fontFamily = FontFamily.SansSerif,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Normal,
                    color = if (isSystemInDarkTheme()) Color(0x80FFFFFF) else Color(0x80000000)
                )
            )
        } else {
            var updateText = ""
            if (isOffline) {
                updateText = stringResource(R.string.text_try_again)
            } else {
                updateText = stringResource(R.string.updates)
            }
            if (updateState == UpdateMonitorImpl.UpdateState.NewVersionAvailable || isOffline) {
                Spacer(modifier = Modifier.weight(1f))
                updateBottom(300, 132, updateText, onUpdateClick)
            }
        }
    }
}

@Composable
private fun phoneView(
    modifier: Modifier,
    version: String,
    isOffline: Boolean,
    updateState: UpdateMonitorImpl.UpdateState,
    onUpdateClick: () -> Unit,
    clickTimeStamps: LinkedList<Long>
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Box(
            modifier = Modifier.padding(top = 201.dp).size(80.dp).clip(RoundedCornerShape(24.dp))
        ) {
            Image(
                painter = painterResource(id = R.drawable.note_logo),
                contentDescription = null,
                modifier = Modifier.size(80.dp),
            )
        }

        Text(
            text = stringResource(R.string.app_name),
            modifier = Modifier.padding(top = 10.dp),
            style = TextStyle(
                fontFamily = FontFamily.SansSerif,
                fontSize = 20.sp,
                fontWeight = FontWeight.W500,
                color = if (isSystemInDarkTheme()) Color(0xe6FFFFFF) else Color(0xe6000000)
            )
        )

        Text(
            text = version,
            style = TextStyle(
                fontFamily = FontFamily.SansSerif,
                fontSize = 12.sp,
                fontWeight = FontWeight.W400,
                color = if (isSystemInDarkTheme()) Color(0x80FFFFFF) else Color(0x80000000)
            ),
            modifier = Modifier
                .padding(top = 8.dp)
                .pointerInput(Unit) {
                    detectTapGestures(
                        onTap = {
                            val currentTime = System.currentTimeMillis()
                            clickTimeStamps.add(currentTime)
                            while (clickTimeStamps.isNotEmpty()
                                && currentTime - clickTimeStamps.first > ENABLE_LOG_TIME_WINDOW
                            ) {
                                clickTimeStamps.removeFirst()
                            }
                            if (clickTimeStamps.size >= ENABLE_LOG_CLICK_TIME) {
                                clickTimeStamps.clear()
                                val isLogEnabled = !Logger.isLogEnabled
                                Logger.isLogEnabled = isLogEnabled
                                ToastUtils.makeWithCancel(
                                    if (isLogEnabled) {
                                        R.string.enable_log
                                    } else {
                                        R.string.disable_log
                                    }
                                )
                            }
                        }
                    )
                }
        )

        if (isOffline) {
            Text(
                text = stringResource(R.string.cannot_check_updates),
                modifier = Modifier.padding(top = 5.dp),
                style = TextStyle(
                    fontFamily = FontFamily.SansSerif,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.W400,
                    color = if (isSystemInDarkTheme()) Color(0x80FFFFFF) else Color(0x80000000)
                )
            )
        }

        if (updateState == UpdateMonitorImpl.UpdateState.LatestVersion && !isOffline) {
            Text(
                text = stringResource(R.string.lastest_version_tips),
                modifier = Modifier.padding(top = 4.dp),
                style = TextStyle(
                    fontFamily = FontFamily.SansSerif,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.W400,
                    color = if (isSystemInDarkTheme()) Color(0x80FFFFFF) else Color(0x80000000)
                )
            )
        } else {
            var updateText = ""
            if (isOffline) {
                updateText = stringResource(R.string.text_try_again)
            } else {
                updateText = stringResource(R.string.updates)
            }
            if (updateState == UpdateMonitorImpl.UpdateState.NewVersionAvailable || isOffline) {
                Spacer(modifier = Modifier.weight(1f))
                updateBottom(280, 40, updateText, onUpdateClick)
            }
        }
    }
}



@Composable
private fun updateBottom(width: Int, bottom: Int, updateText: String, onUpdateClick: () -> Unit) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = bottom.dp),
        contentAlignment = Alignment.Center
    ) {
        Row(
            modifier = Modifier
                .width(width.dp)
                .height(44.dp)
                .clip(RoundedCornerShape(30.dp))
                .clickableHover(
                    onClick = onUpdateClick,
                    role = Role.Button
                )
                .background(
                    colorResource(id = R.color.home_category_add_btn_bg)
                ),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Text(
                text = updateText,
                color = colorResource(R.color.search_highlight),
                style = HomeCategoryAddCategoryTextStyle,
                modifier = Modifier.padding(horizontal = 16.dp),
                maxLines = 1,
                fontWeight = FontWeight.W500,
                fontSize = 16.sp,
                overflow = TextOverflow.Ellipsis,
            )
        }
    }
}




@Preview
@Composable
fun VersionScreenPreview() {
    VersionScreen(
        onBackClick = {},
        onUpdateClick = {},
        version = "V1.0.000.12345",
        isLoading = false,
        isOffline = false,
        updateState = UpdateMonitorImpl.UpdateState.Idle
    )
}

private const val ENABLE_LOG_CLICK_TIME = 7
private const val ENABLE_LOG_TIME_WINDOW = 4000L