package com.tcl.ai.note.home.vm

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.home.model.HomeCategoryItemModel
import com.tcl.ai.note.home.vm.action.HomeCategoryAction
import com.tcl.ai.note.home.vm.action.HomeCategoryEvent
import com.tcl.ai.note.home.vm.state.HomeTitleMode
import com.tcl.ai.note.home.vm.action.HomeNoteListAction
import kotlinx.coroutines.launch

/**
协调UI和多个ViewModel之间的交互
homeNoteViewModel  负责处理主页笔记相关的逻辑
homeCategoryViewModel 负责处理主页分类相关的逻辑
 */
class HomeCoordinator(
    val homeCategoryViewModel: HomeCategoryViewModel,
    val homeNoteViewModel: HomeNoteViewModel
) {
    val homeUiState = homeNoteViewModel.homeNoteUiState
    val categoryUiState = homeCategoryViewModel.homeCategoryUiState
    val categoryEffect = homeCategoryViewModel.effect

    init {
        homeCategoryViewModel.viewModelScope.launch {
            categoryEffect.collect { effect ->
                when (effect) {
                    is HomeCategoryEvent.OnCategoryChange -> {
                        onCategoryChangedToNoteList(effect.changeCategoryItem)
                    }

                    else -> {}
                }
            }
        }
    }
    /**
     * 处理笔记列表相关操作
     */
    fun handleHomeAction(action: HomeNoteListAction) {
        homeNoteViewModel.onHomeAction(action)
        if(action is HomeNoteListAction.OnChangeTitleMode){
            // 根据标题模式变化调用分类状态变更
            val isSearchMode = action.titleMode == HomeTitleMode.Search
            homeCategoryViewModel.onChangeCategoryState(isSearchMode)
        }
    }
    /**
     * 处理分类相关操作
     */
    fun handleCategoryAction(action: HomeCategoryAction) {
        homeCategoryViewModel.onCategoryAction(action)
        when (action) {
            is HomeCategoryAction.OnCategorySelected -> {
                onCategoryChangedToNoteList(action.category)
            }

            is HomeCategoryAction.OnRenameCategoryClick  -> {
                homeNoteViewModel.onCategoryDialogShow(action.isShowDialog)
            }

            is HomeCategoryAction.OnCreateNewCategoryClick -> {
                homeNoteViewModel.onCategoryDialogShow(action.isShowDialog)
            }

            else -> {}
        }
    }

    /**
     * 分类变更
     */
    private fun onCategoryChangedToNoteList(categoryItem: HomeCategoryItemModel) {
        homeNoteViewModel.onCategoryChanged(categoryItem)
    }

    /**
     * 停止数据库监听
     */
    fun stopObservingCategoryAndNotesList() {
//        homeCategoryViewModel.stopObservingCategoryList()
//        homeNoteViewModel.stopObservingNotes()
    }

    fun observeCategoryAndNotesList() {
//        homeCategoryViewModel.observeCategoryList()
//        homeNoteViewModel.observeNoteListFromDatabase()
    }

}

@Composable
fun rememberHomeCoordinator(
    categoryViewModel: HomeCategoryViewModel = hiltViewModel(),
    homeNoteViewModel: HomeNoteViewModel = hiltViewModel()
): HomeCoordinator {
    return remember(homeNoteViewModel, categoryViewModel) {
        HomeCoordinator(
            homeNoteViewModel = homeNoteViewModel,
            homeCategoryViewModel = categoryViewModel
        )
    }
}