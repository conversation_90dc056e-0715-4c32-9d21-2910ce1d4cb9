package com.tcl.ai.note.home.model

/**
 * 笔记内容类型判断
 */
data class NoteContentInfo(
    val hasTitle: Boolean,
    val hasContent: Boolean,
    val hasImage: <PERSON>olean,
    val hasHandwriting: <PERSON><PERSON><PERSON>,
    val hasAudio: <PERSON><PERSON><PERSON>,
    val cleanContent: String
)

/**
 * 获取笔记显示信息
 */
data class NoteDisplayInfo(
    val thumbnailType: ThumbnailType,
    val primaryTitle: String,
    val titleResId: Int? = null,
    val secondaryInfo: String,//副标题 显示date
    val showAudioIcon: Boolean
)