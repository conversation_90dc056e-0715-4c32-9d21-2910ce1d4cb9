package com.tcl.ai.note.home.model

import com.tcl.ai.note.handwritingtext.richtext.data.RichTextStyleEntity

/**
 * 首页笔记项数据模型
 */
data class HomeNoteItemModel(
    val noteId: String,
    val noteTitle: String,
    val summary: String? = null,
    val content: String?=null,
    val richTextStyleEntity: RichTextStyleEntity? = null,//二期富文本样式
    val titleResId: Int? = null,
    val date: String,
    val isChecked: Boolean = false,
    val categoryId: String = "", // 分类ID
    val categoryName: String? = null, // 分类名称
    val createTime: Long?=null,
    val modifyTime: Long?=null,
    val searchKey: String = "", // 新增字段，表示搜索关键字
    // 新增：高亮信息
    val highlightInfo: HighlightInfo? = null,
    val categoryIcon: CategoryIcon? = null,
    // 缩略图相关信息（封装）
    val thumbnailInfo: ThumbnailInfo = ThumbnailInfo()
) {
    // 为了保持向后兼容，提供便捷访问属性
    val thumbnailType: ThumbnailType get() = thumbnailInfo.type
    val showAudioIcon: Boolean get() = thumbnailInfo.showAudioIcon
    val image: String? get() = thumbnailInfo.image
    val handwritingThumbnail: String? get() = thumbnailInfo.handwritingThumbnail
    val handwritingThumbnailDark: String? get() = thumbnailInfo.handwritingThumbnailDark
    val firstPicture: String? get() = thumbnailInfo.firstPicture
    val hasAudio: Boolean get() = thumbnailInfo.hasAudio
}

