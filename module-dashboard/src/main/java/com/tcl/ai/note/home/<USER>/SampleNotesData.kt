package com.tcl.ai.note.home.data

import com.tcl.ai.note.base.R
import com.tcl.ai.note.home.model.CategoryIcon
import com.tcl.ai.note.home.model.HomeCategoryItemModel
import com.tcl.ai.note.home.model.HomeNoteItemModel
import com.tcl.ai.note.home.model.ThumbnailInfo
import com.tcl.ai.note.home.model.ThumbnailType
import com.tcl.ai.note.home.vm.state.HomeContentListState

// 模拟数据
fun getSampleNotes(): List<HomeNoteItemModel> {
    return listOf(
        HomeNoteItemModel(
            noteId = "1",
            noteTitle = "This is a title,this i...",
            date = "16:51",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.PURE_TEXT)
        ),
        HomeNoteItemModel(
            noteId = "2",
            noteTitle = "Audio",
            date = "14:00 🔊",
            thumbnailInfo = ThumbnailInfo(
                type = ThumbnailType.AUDIO_ICON,
                showAudioIcon = true,
                hasAudio = true
            )
        ),
        HomeNoteItemModel(
            noteId = "3",
            noteTitle = "Image",
            date = "29 APR 2025",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN)
        ),

        HomeNoteItemModel(
            noteId = "5",
            noteTitle = "Image",
            date = "29 APR 2025",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN)
        ),
        HomeNoteItemModel(
            noteId = "6",
            noteTitle = "Image",
            date = "29 APR 2025",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN)
        ),
        HomeNoteItemModel(
            noteId = "7",
            noteTitle = "Image",
            date = "29 APR 2025",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN)
        ),

        HomeNoteItemModel(
            noteId = "9",
            noteTitle = "Image",
            date = "29 APR 2025",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN)
        ),
        HomeNoteItemModel(
            noteId = "10",
            noteTitle = "Image",
            date = "29 APR 2025",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN)
        ),
        HomeNoteItemModel(
            noteId = "11",
            noteTitle = "Image",
            date = "29 APR 2025",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN)
        ),
        HomeNoteItemModel(
            noteId = "12",
            noteTitle = "Image",
            date = "29 APR 2025",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN)
        ),
        HomeNoteItemModel(
            noteId = "13",
            noteTitle = "Image",
            date = "29 APR 2025",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN)
        )
    )
}

 fun homeDemoContentListState() = HomeContentListState(
     notes = getSampleNotes()
)
val demoCategories = listOf(
    HomeCategoryItemModel(
        name = "All notes",
        noteCounts = 30,
        categoryIcon = CategoryIcon(R.drawable.ic_all_notes),
        id = "all_notes",
        isSelected = true
    ),
    HomeCategoryItemModel(
        name = "Uncategorised",
        noteCounts = 26,
        categoryIcon = CategoryIcon(R.drawable.ic_category_uncategorised),
        id = "uncategorised"
    ),
    HomeCategoryItemModel(
        name = "Recently Deleted",
        noteCounts = 60,
        categoryIcon = CategoryIcon(R.drawable.ic_category_uncategorised),
        id = "recently_deleted"
    ),
)

